package com.collabhub.be.modules.notifications.controller;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.notifications.service.NotificationPreferenceService;
import com.collabhub.be.modules.notifications.service.UnsubscribeTokenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;


/**
 * REST controller for handling email notification unsubscribe requests.
 * Provides secure one-click unsubscribe functionality for email notifications
 * while preserving critical emails like invitations and account security.
 */
@RestController
@RequestMapping("/api/notifications/unsubscribe")
@Tag(name = "Email Unsubscribe", description = "One-click email notification unsubscribe functionality")
public class UnsubscribeController {

    private static final Logger logger = LoggerFactory.getLogger(UnsubscribeController.class);

    // Log messages
    private static final String PROCESSING_UNSUBSCRIBE_LOG = "Processing unsubscribe request for token: {}";
    private static final String UNSUBSCRIBE_SUCCESS_LOG = "Successfully unsubscribed email: {}";
    private static final String UNSUBSCRIBE_FAILED_LOG = "Failed to process unsubscribe for token: {}";

    // Error messages
    private static final String MISSING_TOKEN_MESSAGE = "Unsubscribe token is required";

    private final UnsubscribeTokenService unsubscribeTokenService;
    private final NotificationPreferenceService notificationPreferenceService;

    public UnsubscribeController(UnsubscribeTokenService unsubscribeTokenService,
                               NotificationPreferenceService notificationPreferenceService) {
        this.unsubscribeTokenService = unsubscribeTokenService;
        this.notificationPreferenceService = notificationPreferenceService;
    }

    /**
     * Processes one-click unsubscribe from email notifications.
     * This endpoint is designed to be called directly from email links.
     * Returns an HTML page with the result for user-friendly experience.
     *
     * @param token the unsubscribe token from the email
     * @param request the HTTP request for audit logging
     * @return ModelAndView with success or error page
     */
    @GetMapping("/process")
    @Operation(summary = "Process one-click unsubscribe", 
               description = "Processes unsubscribe token and disables email notifications for the user")
    public ModelAndView processUnsubscribe(
            @Parameter(description = "Unsubscribe token from email", required = true)
            @RequestParam("token") String token,
            HttpServletRequest request) {

        if (token == null || token.trim().isEmpty()) {
            logger.warn("Unsubscribe request received without token from IP: {}", getClientIpAddress(request));
            return createErrorView("Missing or invalid unsubscribe token");
        }



        logger.info(PROCESSING_UNSUBSCRIBE_LOG, truncateTokenForLogging(token));

        try {
            // Validate token and get email
            UnsubscribeTokenService.UnsubscribeTokenValidationResult result = 
                    unsubscribeTokenService.validateUnsubscribeToken(token);

            // Disable notification emails for this email address
            notificationPreferenceService.unsubscribeFromNotificationEmails(result.email());

            // Log successful unsubscribe with audit information
            logUnsubscribeSuccess(result.email(), request);

            logger.info(UNSUBSCRIBE_SUCCESS_LOG, result.email());

            return createSuccessView(result.email());

        } catch (Exception e) {
            logger.warn(UNSUBSCRIBE_FAILED_LOG, truncateTokenForLogging(token), e);
            return createErrorView("Invalid or expired unsubscribe token");
        }
    }

    /**
     * API endpoint for programmatic unsubscribe (for future use).
     * Returns JSON response instead of HTML page.
     *
     * @param token the unsubscribe token
     * @param request the HTTP request for audit logging
     * @return ResponseEntity with success or error status
     */
    @PostMapping("/api")
    @Operation(summary = "API unsubscribe endpoint", 
               description = "Programmatic unsubscribe endpoint returning JSON response")
    public ResponseEntity<Map<String, Object>> apiUnsubscribe(
            @Parameter(description = "Unsubscribe token", required = true)
            @RequestParam("token") String token,
            HttpServletRequest request) {

        if (token == null || token.trim().isEmpty()) {
            throw new BadRequestException(ErrorCode.VALIDATION_FAILED, MISSING_TOKEN_MESSAGE);
        }



        logger.info(PROCESSING_UNSUBSCRIBE_LOG, truncateTokenForLogging(token));

        try {
            // Validate token and get email
            UnsubscribeTokenService.UnsubscribeTokenValidationResult result = 
                    unsubscribeTokenService.validateUnsubscribeToken(token);

            // Disable notification emails for this email address
            notificationPreferenceService.unsubscribeFromNotificationEmails(result.email());

            // Log successful unsubscribe with audit information
            logUnsubscribeSuccess(result.email(), request);

            logger.info(UNSUBSCRIBE_SUCCESS_LOG, result.email());

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "Successfully unsubscribed from email notifications",
                    "email", result.email()
            ));

        } catch (Exception e) {
            logger.warn(UNSUBSCRIBE_FAILED_LOG, truncateTokenForLogging(token), e);
            throw e; // Let global exception handler deal with it
        }
    }

    /**
     * Creates a success view for successful unsubscribe.
     *
     * @param email the email that was unsubscribed
     * @return ModelAndView for success page
     */
    private ModelAndView createSuccessView(String email) {
        ModelAndView modelAndView = new ModelAndView("unsubscribe/success");
        modelAndView.addObject("email", email);
        modelAndView.addObject("title", "Unsubscribed Successfully");
        return modelAndView;
    }

    /**
     * Creates an error view for failed unsubscribe.
     *
     * @param errorMessage the error message to display
     * @return ModelAndView for error page
     */
    private ModelAndView createErrorView(String errorMessage) {
        ModelAndView modelAndView = new ModelAndView("unsubscribe/error");
        modelAndView.addObject("errorMessage", errorMessage);
        modelAndView.addObject("title", "Unsubscribe Failed");
        return modelAndView;
    }

    /**
     * Extracts client IP address from request, handling proxy headers.
     *
     * @param request the HTTP request
     * @return the client IP address
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * Checks if the client IP is within rate limits for unsubscribe attempts.
     *
     * @param clientIp the client IP address
     * @return true if within rate limits, false otherwise
     */
    private boolean isWithinRateLimit(String clientIp) {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);

        // Get or create attempt history for this IP
        List<LocalDateTime> attempts = ipAttemptHistory.computeIfAbsent(clientIp, k -> new ArrayList<>());

        // Remove attempts older than 1 hour
        attempts.removeIf(timestamp -> timestamp.isBefore(oneHourAgo));

        // Check if under rate limit
        if (attempts.size() >= MAX_UNSUBSCRIBE_ATTEMPTS_PER_IP_PER_HOUR) {
            return false;
        }

        // Record this attempt
        attempts.add(LocalDateTime.now());
        return true;
    }

    /**
     * Cleans up old rate limiting entries to prevent memory leaks.
     * Should be called periodically by a scheduled task.
     */
    public void cleanupRateLimitHistory() {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);

        ipAttemptHistory.entrySet().removeIf(entry -> {
            List<LocalDateTime> attempts = entry.getValue();
            attempts.removeIf(timestamp -> timestamp.isBefore(oneHourAgo));
            return attempts.isEmpty();
        });

        logger.debug("Cleaned up rate limit history, {} IPs remaining", ipAttemptHistory.size());
    }

    /**
     * Enhanced audit logging with additional security context.
     *
     * @param email the email that was unsubscribed
     * @param request the HTTP request for audit data
     */
    private void logUnsubscribeSuccess(String email, HttpServletRequest request) {
        String ipAddress = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String referer = request.getHeader("Referer");
        String xForwardedFor = request.getHeader("X-Forwarded-For");

        // Enhanced audit log with security context
        logger.info("AUDIT: Email unsubscribe successful - email: {}, ip: {}, userAgent: {}, referer: {}, xForwardedFor: {}, timestamp: {}",
                   email, ipAddress, userAgent, referer, xForwardedFor, LocalDateTime.now());

        // Additional security logging for monitoring
        logger.info("SECURITY: Unsubscribe action completed - email_hash: {}, source_ip: {}",
                   hashEmail(email), ipAddress);
    }

    /**
     * Creates a hash of the email for security logging without exposing the actual email.
     *
     * @param email the email to hash
     * @return hashed email for logging
     */
    private String hashEmail(String email) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(email.getBytes(java.nio.charset.StandardCharsets.UTF_8));
            return java.util.Base64.getEncoder().encodeToString(hash).substring(0, 16);
        } catch (Exception e) {
            return "hash_error";
        }
    }

    /**
     * Truncates token for safe logging without exposing full token value.
     *
     * @param token the token to truncate
     * @return truncated token for logging
     */
    private String truncateTokenForLogging(String token) {
        if (token == null || token.length() <= 8) {
            return "***";
        }
        return token.substring(0, 8) + "...";
    }
}
