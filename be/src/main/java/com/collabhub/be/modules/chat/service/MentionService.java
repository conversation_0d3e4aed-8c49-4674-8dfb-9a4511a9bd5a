package com.collabhub.be.modules.chat.service;

import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.chat.dto.MentionDto;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service for parsing and validating @mentions in chat messages.
 * Handles mention parsing, participant matching, and validation.
 */
@Service
@Validated
public class MentionService {

    private static final Logger logger = LoggerFactory.getLogger(MentionService.class);

    // Constants
    // Pattern to detect mentions: @pavel, @phristov.nl, @Pavel, @Desi, etc.
    // Supports email prefixes, names, and first names for comprehensive matching
    private static final Pattern MENTION_PATTERN = Pattern.compile("@([a-zA-Z0-9._-]+)");
    private static final String EMAIL_SEPARATOR = "@";
    private static final String UNKNOWN_USER_FALLBACK = "Unknown User";
    private static final String EXTERNAL_USER_FALLBACK = "External User";
    
    private final HubParticipantRepositoryImpl participantRepository;
    private final UserRepository userRepository;

    public MentionService(HubParticipantRepositoryImpl participantRepository, UserRepository userRepository) {
        this.participantRepository = participantRepository;
        this.userRepository = userRepository;
    }

    /**
     * Parses @mentions from message content and validates them against hub participants.
     *
     * @param content the message content
     * @param hubId the hub ID to validate participants against
     * @return list of valid mentions with participant details
     */
    @Transactional(readOnly = true)
    public List<MentionDto> parseMentions(String content, @NotNull Long hubId) {
        if (content == null || content.trim().isEmpty()) {
            return new ArrayList<>();
        }

        List<HubParticipant> hubParticipants = participantRepository.findActiveParticipantsByHubId(hubId);
        Map<String, HubParticipant> participantLookup = createParticipantLookupMap(hubParticipants);

        return extractMentionsFromContent(content, participantLookup, hubId);
    }

    /**
     * Creates participant lookup map for efficient mention matching.
     * Maps email addresses, email prefixes, and names for comprehensive matching.
     */
    private Map<String, HubParticipant> createParticipantLookupMap(List<HubParticipant> participants) {
        Map<String, HubParticipant> lookupMap = new HashMap<>();

        for (HubParticipant participant : participants) {
            // Primary lookup by full email address
            if (participant.getEmail() != null && !participant.getEmail().trim().isEmpty()) {
                String email = participant.getEmail().toLowerCase().trim();
                lookupMap.put(email, participant);
            }

            // Secondary lookup by email prefix (part before @) - HIGHEST PRIORITY for uniqueness
            String emailPrefix = getEmailPrefix(participant.getEmail());
            if (emailPrefix != null && !emailPrefix.trim().isEmpty()) {
                String normalizedPrefix = emailPrefix.toLowerCase().trim();
                // Email prefix should take absolute priority - overwrite any existing entries
                lookupMap.put(normalizedPrefix, participant);
            }

            // Fallback lookup by participant name (ONLY if no email prefix conflict)
            String participantName = getParticipantDisplayName(participant);
            if (participantName != null && !participantName.trim().isEmpty()) {
                String normalizedName = participantName.toLowerCase().trim();
                // Only add if not already present (email-based lookup takes priority)
                lookupMap.putIfAbsent(normalizedName, participant);

                // Also add lookup by first name only (LOWEST priority)
                String[] nameParts = normalizedName.split("\\s+");
                if (nameParts.length > 0) {
                    String firstName = nameParts[0];
                    lookupMap.putIfAbsent(firstName, participant);
                }
            }
        }

        return lookupMap;
    }

    /**
     * Gets email prefix (part before @) for fallback lookup.
     */
    private String getEmailPrefix(String email) {
        if (email == null || !email.contains(EMAIL_SEPARATOR)) {
            return null;
        }
        return email.substring(0, email.indexOf(EMAIL_SEPARATOR));
    }

    /**
     * Extracts mentions from content using email-based participant lookup.
     */
    private List<MentionDto> extractMentionsFromContent(String content,
                                                       Map<String, HubParticipant> participantLookup,
                                                       Long hubId) {
        List<MentionDto> mentions = new ArrayList<>();
        Matcher matcher = MENTION_PATTERN.matcher(content);

        while (matcher.find()) {
            // Extract email or email prefix from mention
            String mentionedEmail = matcher.group(1).toLowerCase().trim();
            processMentionMatch(mentionedEmail, participantLookup, mentions, hubId);
        }

        return mentions;
    }

    /**
     * Processes a single email-based mention match.
     */
    private void processMentionMatch(String mentionedEmail,
                                   Map<String, HubParticipant> participantLookup,
                                   List<MentionDto> mentions,
                                   Long hubId) {
        HubParticipant matchedParticipant = participantLookup.get(mentionedEmail);

        if (matchedParticipant != null && !isDuplicateMention(mentions, matchedParticipant.getId())) {
            MentionDto mention = createMentionDto(matchedParticipant);
            mentions.add(mention);
            logValidMention(mentionedEmail, matchedParticipant.getId());
        } else if (matchedParticipant == null) {
            logInvalidMention(mentionedEmail, hubId);
        }
    }

    /**
     * Checks if mention is already in the list.
     */
    private boolean isDuplicateMention(List<MentionDto> mentions, Long participantId) {
        return mentions.stream()
                .anyMatch(m -> m.getParticipantId().equals(participantId));
    }

    /**
     * Creates MentionDto from participant.
     */
    private MentionDto createMentionDto(HubParticipant participant) {
        String participantName = getParticipantDisplayName(participant);
        return new MentionDto(
                participant.getId(),
                participantName,
                participant.getEmail(),
                participant.getIsExternal()
        );
    }

    /**
     * Logs valid mention found.
     */
    private void logValidMention(String mentionedEmail, Long participantId) {
        logger.debug("Found valid email mention: {} -> participant {}", mentionedEmail, participantId);
    }

    /**
     * Logs invalid mention found.
     */
    private void logInvalidMention(String mentionedEmail, Long hubId) {
        logger.debug("Invalid email mention found: {} (no matching participant in hub {})",
                mentionedEmail, hubId);
    }

    /**
     * Gets display name for a participant.
     */
    private String getParticipantDisplayName(HubParticipant participant) {
        if (participant.getUserId() != null) {
            return getInternalUserDisplayName(participant);
        } else {
            return getExternalUserDisplayName(participant);
        }
    }

    /**
     * Gets display name for internal user.
     */
    private String getInternalUserDisplayName(HubParticipant participant) {
        User user = userRepository.findById(participant.getUserId());
        if (user != null && user.getDisplayName() != null) {
            return user.getDisplayName();
        }
        return getEmailPrefixFallback(participant, UNKNOWN_USER_FALLBACK);
    }

    /**
     * Gets display name for external user.
     */
    private String getExternalUserDisplayName(HubParticipant participant) {
        if (participant.getName() != null) {
            return participant.getName();
        }
        return getEmailPrefixFallback(participant, EXTERNAL_USER_FALLBACK);
    }

    /**
     * Gets email prefix as fallback display name.
     */
    private String getEmailPrefixFallback(HubParticipant participant, String defaultFallback) {
        if (participant.getEmail() != null) {
            return participant.getEmail().substring(0, participant.getEmail().indexOf(EMAIL_SEPARATOR));
        }
        return defaultFallback;
    }

    /**
     * Validates that all mentioned participant IDs exist and are active in the hub.
     *
     * @param mentionedParticipantIds list of participant IDs to validate
     * @param hubId the hub ID
     * @return true if all participants are valid, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean validateMentions(List<Long> mentionedParticipantIds, @NotNull Long hubId) {
        if (mentionedParticipantIds == null || mentionedParticipantIds.isEmpty()) {
            return true;
        }

        List<Long> validParticipantIds = getValidParticipantIds(hubId);
        return validParticipantIds.containsAll(mentionedParticipantIds);
    }

    /**
     * Gets list of valid participant IDs for hub.
     */
    private List<Long> getValidParticipantIds(Long hubId) {
        List<HubParticipant> hubParticipants = participantRepository.findActiveParticipantsByHubId(hubId);
        return hubParticipants.stream()
                .map(HubParticipant::getId)
                .toList();
    }
}
