package com.collabhub.be.modules.auth.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;

/**
 * Service for sending emails including verification emails, magic links, and notifications.
 * Uses Spring Boot's built-in email support with SMTP configuration and Thymeleaf templates.
 */
@Service
public class EmailService {

    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);

    private final JavaMailSender mailSender;
    private final TemplateEngine templateEngine;
    private final String baseUrl;
    private final String frontendUrl;

    public EmailService(JavaMailSender mailSender,
                       TemplateEngine templateEngine,
                       @Value("${app.base-url}") String baseUrl,
                       @Value("${app.frontend-url}") String frontendUrl) {
        this.mailSender = mailSender;
        this.templateEngine = templateEngine;
        this.baseUrl = baseUrl;
        this.frontendUrl = frontendUrl;
    }

    /**
     * Sends an email verification message to the user using Thymeleaf template.
     *
     * @param email the recipient email address
     * @param displayName the user's display name
     * @param verificationToken the verification token
     * @return true if email was sent successfully, false otherwise
     */
    public boolean sendEmailVerification(String email, String displayName, String verificationToken) {
        try {
            String verificationUrl = frontendUrl + "/auth/verify-email?token=" + verificationToken;

            // Create Thymeleaf context with variables
            Context context = new Context();
            context.setVariable("displayName", displayName);
            context.setVariable("verificationUrl", verificationUrl);
            context.setVariable("frontendUrl", frontendUrl);
            context.setVariable("subject", "Welcome to Collaboration Hub - Verify Your Email");

            // Process the template
            String htmlContent = templateEngine.process("email/email-verification", context);

            // Create and send the email
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            helper.setTo(email);
            helper.setSubject("Welcome to Collaboration Hub - Verify Your Email");
            helper.setFrom("<EMAIL>");
            helper.setText(htmlContent, true); // true indicates HTML content

            mailSender.send(mimeMessage);
            logger.info("Email verification sent successfully to: {}", email);
            return true;

        } catch (MailException | MessagingException e) {
            logger.error("Failed to send email verification to: {}", email, e);
            return false;
        }
    }

    /**
     * Sends a magic link for external user authentication using Thymeleaf template.
     *
     * @param email the recipient email address
     * @param magicToken the magic link token
     * @param hubName the collaboration hub name
     * @return true if email was sent successfully, false otherwise
     */
    public boolean sendMagicLink(String email, String magicToken, String hubName) {
        return sendMagicLink(email, magicToken, hubName, null, null, null, null);
    }

    /**
     * Sends a magic link for external user authentication with enhanced context.
     *
     * @param email the recipient email address
     * @param magicToken the magic link token
     * @param hubName the collaboration hub name
     * @param inviterName the name of the person who sent the invitation (optional)
     * @param participantRole the role the participant will have (optional)
     * @param accountName the account/company name for branding (optional)
     * @param hubDescription brief description of the project (optional)
     * @return true if email was sent successfully, false otherwise
     */
    public boolean sendMagicLink(String email, String magicToken, String hubName,
                                String inviterName, String participantRole,
                                String accountName, String hubDescription) {
        try {
            String magicUrl = buildMagicLinkUrl(magicToken);
            String subject = buildEmailSubject(hubName, accountName);

            // Create Thymeleaf context with enhanced variables
            Context context = createMagicLinkEmailContext(
                hubName, magicUrl, subject, inviterName,
                participantRole, accountName, hubDescription
            );

            // Process the template
            String htmlContent = templateEngine.process("email/magic-link", context);

            // Create and send the email
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            helper.setTo(email);
            helper.setSubject(subject);
            helper.setFrom("<EMAIL>");
            helper.setText(htmlContent, true); // true indicates HTML content

            mailSender.send(mimeMessage);
            logger.info("Magic link sent successfully to: {}", email);
            return true;

        } catch (MailException | MessagingException e) {
            logger.error("Failed to send magic link to: {}", email, e);
            return false;
        }
    }

    /**
     * Sends a simple text email without template processing.
     *
     * @param email the recipient email address
     * @param subject the email subject
     * @param body the email body (plain text)
     * @return true if email was sent successfully, false otherwise
     */
    public boolean sendSimpleEmail(String email, String subject, String body) {
        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            helper.setTo(email);
            helper.setSubject(subject);
            helper.setFrom("<EMAIL>");
            helper.setText(body, false); // false indicates plain text content

            mailSender.send(mimeMessage);
            logger.info("Simple email sent successfully to: {}", email);
            return true;

        } catch (MailException | MessagingException e) {
            logger.error("Failed to send simple email to: {}", email, e);
            return false;
        }
    }

    /**
     * Builds the magic link URL with proper formatting.
     */
    private String buildMagicLinkUrl(String magicToken) {
        return frontendUrl + "/auth/magic-link?token=" + magicToken;
    }

    /**
     * Builds the email subject with optional account branding.
     */
    private String buildEmailSubject(String hubName, String accountName) {
        if (accountName != null && !accountName.trim().isEmpty()) {
            return String.format("Invitation to collaborate on %s - %s", hubName, accountName);
        }
        return "Access Your Collaboration Hub - " + hubName;
    }

    /**
     * Creates the Thymeleaf context with all magic link email variables.
     */
    private Context createMagicLinkEmailContext(String hubName, String magicUrl, String subject,
                                              String inviterName, String participantRole,
                                              String accountName, String hubDescription) {
        Context context = new Context();

        // Required variables
        context.setVariable("hubName", hubName);
        context.setVariable("magicUrl", magicUrl);
        context.setVariable("frontendUrl", frontendUrl);
        context.setVariable("subject", subject);

        // Enhanced context variables (with null-safe defaults)
        context.setVariable("inviterName", inviterName != null ? inviterName : "");
        context.setVariable("participantRole", formatParticipantRole(participantRole));
        context.setVariable("accountName", accountName != null ? accountName : "Collaboration Hub");
        context.setVariable("hubDescription", hubDescription != null ? hubDescription : "");
        context.setVariable("hasInviter", inviterName != null && !inviterName.trim().isEmpty());
        context.setVariable("hasRole", participantRole != null && !participantRole.trim().isEmpty());
        context.setVariable("hasDescription", hubDescription != null && !hubDescription.trim().isEmpty());

        return context;
    }

    /**
     * Formats participant role for display in email.
     */
    private String formatParticipantRole(String role) {
        if (role == null || role.trim().isEmpty()) {
            return "";
        }

        return switch (role.toLowerCase()) {
            case "admin" -> "Administrator";
            case "content_creator" -> "Content Creator";
            case "reviewer" -> "Reviewer";
            case "reviewer_creator" -> "Reviewer & Content Creator";
            default -> role.substring(0, 1).toUpperCase() + role.substring(1).toLowerCase();
        };
    }
}
