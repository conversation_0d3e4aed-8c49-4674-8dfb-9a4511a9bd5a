package com.collabhub.be.modules.notifications.service;

import com.collabhub.be.modules.notifications.dto.NotificationChannel;
import com.collabhub.be.modules.notifications.dto.NotificationMetadata;
import com.collabhub.be.modules.notifications.dto.NotificationType;
import com.collabhub.be.modules.notifications.dto.NotificationUrgency;
import com.collabhub.be.modules.notifications.repository.NotificationPreferenceRepository;
import com.collabhub.be.modules.notifications.repository.NotificationRepository;
import org.jooq.generated.tables.pojos.Notification;
import org.jooq.generated.tables.pojos.NotificationPreference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration test for the notification system.
 * Tests the complete flow from event to notification delivery.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class NotificationSystemIntegrationTest {

    @Autowired
    private NotificationDispatcherService notificationDispatcherService;

    @Autowired
    private NotificationPreferenceRepository preferenceRepository;

    @Autowired
    private NotificationRepository notificationRepository;

    private static final Long TEST_USER_ID = 999L;
    private static final Long TEST_HUB_ID = 888L;

    @BeforeEach
    void setUp() {
        // Clean up any existing test data
        cleanupTestData();
        
        // Set up test preferences - user wants both in-app and email notifications
        createTestPreference(TEST_USER_ID, NotificationType.INVITE_TO_HUB, NotificationChannel.IN_APP, true);
        createTestPreference(TEST_USER_ID, NotificationType.INVITE_TO_HUB, NotificationChannel.EMAIL, true);
    }

    @Test
    void shouldCreateNotificationWhenUserInvitedToHub() {
        // Given
        String title = "Invitation to Collaboration Hub";
        String message = "John Doe invited you to join the collaboration hub 'Summer Campaign'";
        List<Long> recipients = List.of(TEST_USER_ID);
        NotificationStorageService.EntityReferences entityRefs = 
                NotificationStorageService.EntityReferences.hub(TEST_HUB_ID);

        // When
        notificationDispatcherService.dispatchToUserIds(
                NotificationType.INVITE_TO_HUB, title, message, recipients, entityRefs, null,
                NotificationUrgency.getDefaultForType(NotificationType.INVITE_TO_HUB));

        // Then
        List<Notification> notifications = notificationRepository.findByUserIdWithPagination(
                TEST_USER_ID, 0, 10, false);
        
        assertThat(notifications).hasSize(1);
        
        Notification notification = notifications.get(0);
        assertThat(notification.getUserId()).isEqualTo(TEST_USER_ID);
        assertThat(notification.getTitle()).isEqualTo(title);
        assertThat(notification.getMessage()).isEqualTo(message);
        assertThat(notification.getCollaborationHubId()).isEqualTo(TEST_HUB_ID);
        assertThat(notification.getStatus().name()).isEqualTo("UNREAD");
    }

    @Test
    void shouldRespectUserPreferences() {
        // Given - user has disabled in-app notifications for this type
        updateTestPreference(TEST_USER_ID, NotificationType.INVITE_TO_HUB, NotificationChannel.IN_APP, false);
        
        String title = "Test Notification";
        String message = "Test message";
        List<Long> recipients = List.of(TEST_USER_ID);

        // When
        notificationDispatcherService.dispatchToUserIds(
                NotificationType.INVITE_TO_HUB, title, message, recipients, null,
                NotificationMetadata.empty(), NotificationUrgency.getDefaultForType(NotificationType.INVITE_TO_HUB));

        // Then - no in-app notification should be created
        List<Notification> notifications = notificationRepository.findByUserIdWithPagination(
                TEST_USER_ID, 0, 10, false);
        
        assertThat(notifications).isEmpty();
    }

    private void createTestPreference(Long userId, NotificationType type, NotificationChannel channel, boolean enabled) {
        NotificationPreference preference = new NotificationPreference();
        preference.setUserId(userId);
        preference.setType(type.toJooqEnum());
        preference.setChannel(channel.toJooqEnum());
        preference.setEnabled(enabled);
        preferenceRepository.insert(preference);
    }

    private void updateTestPreference(Long userId, NotificationType type, NotificationChannel channel, boolean enabled) {
        // For simplicity, delete and recreate using proper jOOQ pattern
        deleteTestPreference(userId, type, channel);
        createTestPreference(userId, type, channel, enabled);
    }

    private void deleteTestPreference(Long userId, NotificationType type, NotificationChannel channel) {
        // Use jOOQ DSL context to delete preferences
        preferenceRepository.configuration().dsl()
                .deleteFrom(org.jooq.generated.Tables.NOTIFICATION_PREFERENCE)
                .where(org.jooq.generated.Tables.NOTIFICATION_PREFERENCE.USER_ID.eq(userId))
                .and(org.jooq.generated.Tables.NOTIFICATION_PREFERENCE.TYPE.eq(type.toJooqEnum()))
                .and(org.jooq.generated.Tables.NOTIFICATION_PREFERENCE.CHANNEL.eq(channel.toJooqEnum()))
                .execute();
    }

    private void cleanupTestData() {
        // Clean up test notifications using proper jOOQ pattern
        notificationRepository.configuration().dsl()
                .deleteFrom(org.jooq.generated.Tables.NOTIFICATION)
                .where(org.jooq.generated.Tables.NOTIFICATION.USER_ID.eq(TEST_USER_ID))
                .execute();

        // Clean up test preferences using proper jOOQ pattern
        preferenceRepository.configuration().dsl()
                .deleteFrom(org.jooq.generated.Tables.NOTIFICATION_PREFERENCE)
                .where(org.jooq.generated.Tables.NOTIFICATION_PREFERENCE.USER_ID.eq(TEST_USER_ID))
                .execute();
    }
}
