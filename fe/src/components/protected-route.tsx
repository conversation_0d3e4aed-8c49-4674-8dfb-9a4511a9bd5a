import { type ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router';
import { useAuth, AuthStatus } from '@/contexts/auth-context';
import { LoadingScreen } from './loading-screen';
import { ROUTES } from '@/router/routes';
import { useTranslations } from '@/lib/i18n/typed-translations';

interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Protected route component that guards access to authenticated-only routes.
 * This provides an additional layer of protection beyond the AuthLayout.
 * 
 * Usage:
 * <ProtectedRoute>
 *   <DashboardPage />
 * </ProtectedRoute>
 */
export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const { status, hasInitialized } = useAuth();
  const location = useLocation();
  const { t, keys } = useTranslations();

  // Show loading screen if auth hasn't been initialized yet
  if (!hasInitialized || status === AuthStatus.LOADING) {
    return fallback || <LoadingScreen message={t(keys.auth.verifying)} />;
  }

  // Redirect to login if not authenticated
  if (status === AuthStatus.UNAUTHENTICATED) {
    return (
      <Navigate 
        to={ROUTES.LOGIN} 
        state={{ from: location }} 
        replace 
      />
    );
  }

  // Render children if authenticated
  return <>{children}</>;
}

export default ProtectedRoute;
