import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Bell, 
  Mail, 
  MessageSquare, 
  Users, 
  FileText, 
  Eye, 
  AtSign
} from 'lucide-react';
import { useNotificationPreferences, useUpdateNotificationPreferences } from '@/hooks/use-notification-preferences';
import { useAuth } from '@/hooks/auth';

import { useDebouncedUpdates } from '@/hooks/use-debounced-updates';
import type {
  components,
  NotificationPreferenceResponseType,
  NotificationPreferenceResponseChannel
} from '@/lib/api/v1';

type NotificationPreferenceUpdateRequest = components['schemas']['NotificationPreferenceUpdateRequest'];

interface PreferenceUpdate {
  type: NotificationPreferenceResponseType;
  channel: NotificationPreferenceResponseChannel;
  enabled: boolean;
}

type PreferenceState = Record<string, boolean>;

// Simplified notification configuration
const NOTIFICATION_TYPES = {
  INVITE_TO_HUB: {
    title: 'Hub invitations',
    description: 'When you\'re invited to join a collaboration hub',
    category: 'Collaboration',
    icon: Users,
    defaultEnabled: true
  },
  ASSIGNED_AS_REVIEWER: {
    title: 'Review assignments',
    description: 'When you\'re assigned to review content',
    category: 'Collaboration', 
    icon: Eye,
    defaultEnabled: true
  },
  CHAT_ADDED: {
    title: 'Added to chat',
    description: 'When you\'re added to a chat channel',
    category: 'Collaboration',
    icon: MessageSquare,
    defaultEnabled: true
  },
  POST_REVIEWED: {
    title: 'Content reviewed',
    description: 'When your content receives a review',
    category: 'Content',
    icon: FileText,
    defaultEnabled: true
  },
  COMMENT_ADDED: {
    title: 'New comments',
    description: 'When someone comments on your content',
    category: 'Content',
    icon: MessageSquare,
    defaultEnabled: true
  },
  COMMENT_MENTION: {
    title: 'Comment mentions',
    description: 'When you\'re mentioned in a comment',
    category: 'Communication',
    icon: AtSign,
    defaultEnabled: true
  },
  CHAT_MENTION: {
    title: 'Chat mentions',
    description: 'When you\'re mentioned in a chat message',
    category: 'Communication',
    icon: AtSign,
    defaultEnabled: true
  }
} as const;

// Simple notification row component - Gmail/Slack style
const NotificationRow = React.memo(({
  config,
  inAppEnabled,
  emailEnabled,
  onInAppToggle,
  onEmailToggle,
  isInternal,
  isUpdating
}: {
  type: string;
  config: typeof NOTIFICATION_TYPES[keyof typeof NOTIFICATION_TYPES];
  inAppEnabled: boolean;
  emailEnabled: boolean;
  onInAppToggle: () => void;
  onEmailToggle: () => void;
  isInternal: boolean;
  isUpdating: boolean;
}) => {
  const Icon = config.icon;

  return (
    <div className="flex items-center justify-between py-4 px-6">
      <div className="flex items-start gap-3 flex-1 min-w-0">
        <Icon className="w-4 h-4 text-muted-foreground mt-1 flex-shrink-0" />
        <div className="min-w-0 flex-1">
          <Label className="text-sm font-medium text-foreground cursor-pointer">
            {config.title}
          </Label>
          <p className="text-xs text-muted-foreground mt-1 leading-relaxed">
            {config.description}
          </p>
        </div>
      </div>
      
      <div className="flex items-center gap-6 ml-4">
        {isInternal && (
          <div className="flex items-center gap-2">
            <Bell className="w-3 h-3 text-muted-foreground" />
            <Switch
              checked={inAppEnabled}
              onCheckedChange={onInAppToggle}
              disabled={isUpdating}
              className="data-[state=checked]:bg-blue-600"
            />
          </div>
        )}
        
        <div className="flex items-center gap-2">
          <Mail className="w-3 h-3 text-muted-foreground" />
          <Switch
            checked={emailEnabled}
            onCheckedChange={onEmailToggle}
            disabled={isUpdating}
            className="data-[state=checked]:bg-blue-600"
          />
        </div>
      </div>
    </div>
  );
});

NotificationRow.displayName = 'NotificationRow';

// Simple category header component
const CategoryHeader = React.memo(({ title, isInternal }: { title: string; isInternal: boolean }) => (
  <div className="flex items-center justify-between py-3 px-6 border-b border-border/50 bg-muted/20">
    <h3 className="text-sm font-semibold text-foreground">{title}</h3>
    <div className="flex items-center gap-6 text-xs text-muted-foreground">
      {isInternal && (
        <div className="flex items-center gap-1">
          <Bell className="w-3 h-3" />
          <span>In-App</span>
        </div>
      )}
      <div className="flex items-center gap-1">
        <Mail className="w-3 h-3" />
        <span>Email</span>
      </div>
    </div>
  </div>
));

CategoryHeader.displayName = 'CategoryHeader';

// Simple loading skeleton
const NotificationPreferencesSkeleton = React.memo(() => (
  <div className="space-y-6">
    <div className="space-y-2">
      <Skeleton className="h-8 w-64" />
      <Skeleton className="h-4 w-96" />
    </div>
    
    {[1, 2, 3].map((i) => (
      <Card key={i} className="p-6">
        <div className="space-y-4">
          <Skeleton className="h-5 w-32" />
          {[1, 2, 3].map((j) => (
            <div key={j} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Skeleton className="w-4 h-4" />
                <div className="space-y-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-48" />
                </div>
              </div>
              <div className="flex gap-6">
                <Skeleton className="w-10 h-5" />
                <Skeleton className="w-10 h-5" />
              </div>
            </div>
          ))}
        </div>
      </Card>
    ))}
  </div>
));

NotificationPreferencesSkeleton.displayName = 'NotificationPreferencesSkeleton';

// Main component - Simple Gmail/Slack style
export default function NotificationPreferences() {
  const { user } = useAuth();
  const { data: preferences, isLoading, error } = useNotificationPreferences();
  const updatePreferences = useUpdateNotificationPreferences();

  const [localState, setLocalState] = useState<PreferenceState>({});

  // Debounced updates
  const { addUpdate, hasPendingUpdates, clearUpdates } = useDebouncedUpdates<PreferenceUpdate>(
    useCallback((updates: PreferenceUpdate[]) => {
      const updateRequest: NotificationPreferenceUpdateRequest = {
        preferences: updates.map(update => ({
          type: update.type as any,
          channel: update.channel as any,
          enabled: update.enabled,
        })),
      };

      updatePreferences.mutate({
        body: updateRequest,
      }, {
        onSuccess: () => {
          clearUpdates();
        },
        onError: () => {
          // Revert changes on error
          setLocalState(prev => {
            const reverted = { ...prev };
            updates.forEach(update => {
              const key = `${update.type}_${update.channel}`;
              reverted[key] = !update.enabled;
            });
            return reverted;
          });
          clearUpdates();
        },
      });
    }, [updatePreferences]),
    300
  );

  // Memoized computations
  const isInternal = useMemo(() => user?.internal === true, [user?.internal]);
  const isUpdating = useMemo(() => hasPendingUpdates(), [hasPendingUpdates]);

  // Initialize local state from API data
  useEffect(() => {
    if (preferences) {
      const state: PreferenceState = {};
      preferences.forEach((pref: components['schemas']['NotificationPreferenceResponse']) => {
        const key = `${pref.type}_${pref.channel}`;
        state[key] = pref.enabled;
      });
      setLocalState(state);
      clearUpdates();
    }
  }, [preferences]);

  // Toggle handler
  const handleToggle = useCallback((type: NotificationPreferenceResponseType, channel: NotificationPreferenceResponseChannel) => {
    const key = `${type}_${channel}`;

    setLocalState(prev => {
      const newValue = !prev[key];
      addUpdate(key, { type, channel, enabled: newValue });
      return { ...prev, [key]: newValue };
    });
  }, [addUpdate]);

  // Group notifications by category
  const categorizedNotifications = useMemo(() => {
    const grouped: Record<string, Array<[string, typeof NOTIFICATION_TYPES[keyof typeof NOTIFICATION_TYPES]]>> = {};

    Object.entries(NOTIFICATION_TYPES).forEach(([type, config]) => {
      if (!grouped[config.category]) {
        grouped[config.category] = [];
      }
      grouped[config.category].push([type, config]);
    });

    return grouped;
  }, []);

  // Early returns after all hooks
  if (isLoading) {
    return <NotificationPreferencesSkeleton />;
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Notification Preferences</CardTitle>
          <CardDescription>
            Failed to load notification preferences. Please try again.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Simple header */}
      <div className="space-y-1">
        <h1 className="text-2xl font-semibold text-foreground">
          Notification Preferences
        </h1>
        <p className="text-sm text-muted-foreground">
          Choose how you want to be notified about activities and updates.
        </p>
      </div>

      {/* Notification categories */}
      <div className="space-y-6">
        {Object.entries(categorizedNotifications).map(([category, notifications]) => (
          <Card key={category} className="overflow-hidden">
            <CardContent className="p-0">
              <CategoryHeader title={category} isInternal={isInternal} />

              <div className="divide-y divide-border/30">
                {notifications.map(([type, config]) => {
                  const inAppKey = `${type}_IN_APP`;
                  const emailKey = `${type}_EMAIL`;

                  return (
                    <NotificationRow
                      key={type}
                      type={type}
                      config={config}
                      inAppEnabled={localState[inAppKey] || false}
                      emailEnabled={localState[emailKey] || false}
                      onInAppToggle={() => handleToggle(
                        type as NotificationPreferenceResponseType,
                        'IN_APP' as NotificationPreferenceResponseChannel
                      )}
                      onEmailToggle={() => handleToggle(
                        type as NotificationPreferenceResponseType,
                        'EMAIL' as NotificationPreferenceResponseChannel
                      )}
                      isInternal={isInternal}
                      isUpdating={isUpdating}
                    />
                  );
                })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Simple status indicator */}
      {isUpdating && (
        <div className="flex items-center justify-center py-2">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <div className="w-3 h-3 border border-blue-500 border-t-transparent rounded-full animate-spin" />
            Saving...
          </div>
        </div>
      )}
    </div>
  );
}
