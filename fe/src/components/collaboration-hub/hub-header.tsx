import { useState, useEffect } from "react"
import { Building2, UserPlus, Edit2, Check, X, Users } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { formatDistanceToNow } from "date-fns"
import { toast } from "sonner"
import { ManageParticipantsDialog } from "./manage-participants-dialog"
import { useUpdateCollaborationHub } from "@/hooks/collaboration-hubs"
import { useHubPermissions } from "@/hooks/collaboration-hubs/use-hub-permissions"
import type { CollaborationHubResponse } from "./types"

interface HubHeaderProps {
  hub: CollaborationHubResponse
}

export function HubHeader({ hub }: HubHeaderProps) {
  const { t, keys } = useTranslations()
  const [isEditingName, setIsEditingName] = useState(false)
  const [editedName, setEditedName] = useState(hub.name)
  const [manageParticipantsOpen, setManageParticipantsOpen] = useState(false)

  const updateHubMutation = useUpdateCollaborationHub()
  const {
    canViewParticipants,
    hasAdminPermissions,
    getParticipantButtonText
  } = useHubPermissions(hub.id)

  // Update editedName when hub name changes (e.g., from cache invalidation)
  useEffect(() => {
    if (!isEditingName) {
      setEditedName(hub.name)
    }
  }, [hub.name, isEditingName])

  const getRoleBadgeVariant = (role?: string) => {
    if (!role) return 'secondary'
    switch (role.toLowerCase()) {
      case 'admin':
        return 'default'
      case 'content_creator':
        return 'secondary'
      case 'reviewer':
        return 'outline'
      case 'reviewer_creator':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  const formatRole = (role?: string) => {
    if (!role) return t(keys.collaborationHubs.unknown)
    return t(keys.collaborationHubs.roles[role as keyof typeof keys.collaborationHubs.roles]) || role
  }


  const handleEditName = () => {
    setIsEditingName(true)
    setEditedName(hub.name)
  }

  const handleSaveName = async () => {
    if (!editedName || !editedName.trim()) {
      toast.error(t(keys.collaborationHubs.updateError))
      return
    }

    if (editedName.trim() === hub.name) {
      setIsEditingName(false)
      return
    }

    try {
      await updateHubMutation.mutateAsync({
        params: { path: { id: hub.id } },
        body: {
          name: editedName.trim(),
          description: hub.description || undefined,
        },
      })

      toast.success(t(keys.collaborationHubs.updateSuccess))
      setIsEditingName(false)
    } catch (__error) {
      // Error handling for hub name update
      toast.error(t(keys.collaborationHubs.updateError))
      // Reset to original name on error
      setEditedName(hub.name)
    }
  }

  const handleCancelEdit = () => {
    setIsEditingName(false)
    setEditedName(hub.name)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSaveName()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      handleCancelEdit()
    }
  }

  const isAdmin = hub.myRole === 'admin'

  return (
    <div className="px-4 md:px-8 py-6 border-b bg-card/50">
      <div className="space-y-6">
        {/* Main Hub Info */}
        <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
          <div className="flex items-start gap-4">
            {/* Brand Logo/Icon */}
            <div className="p-3 bg-primary/10 rounded-xl">
              <Building2 className="h-8 w-8 text-primary" />
            </div>

            <div className="space-y-2 flex-1">
              <div className="flex items-center gap-3 flex-wrap">
                {isEditingName ? (
                  <div className="flex items-center gap-2">
                    <Input
                      value={editedName}
                      onChange={(e) => setEditedName(e.target.value)}
                      onKeyDown={handleKeyDown}
                      className="text-2xl md:text-3xl font-bold h-auto py-1 px-2"
                      autoFocus
                      disabled={updateHubMutation.isPending}
                      placeholder={updateHubMutation.isPending ? t(keys.collaborationHubs.updating) : undefined}
                    />
                    <Button
                      size="sm"
                      onClick={handleSaveName}
                      disabled={updateHubMutation.isPending}
                    >
                      <Check className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={updateHubMutation.isPending}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center gap-3">
                    <h1 className="text-2xl md:text-3xl font-bold tracking-tight">{hub.name}</h1>
                    {isAdmin && (
                      <Button size="sm" variant="ghost" onClick={handleEditName}>
                        <Edit2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                )}
                <Badge variant={getRoleBadgeVariant(hub.myRole)}>
                  {formatRole(hub.myRole)}
                </Badge>
              </div>

              {hub.brandName && (
                <p className="text-lg text-muted-foreground">{hub.brandName}</p>
              )}

              {hub.description && (
                <p className="text-muted-foreground max-w-2xl">{hub.description}</p>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2 flex-wrap">
            {canViewParticipants && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setManageParticipantsOpen(true)}
              >
                {hasAdminPermissions ? (
                  <UserPlus className="h-4 w-4 mr-2" />
                ) : (
                  <Users className="h-4 w-4 mr-2" />
                )}
                <span className="hidden sm:inline">
                  {hasAdminPermissions
                    ? t(keys.collaborationHubs.invite)
                    : t(keys.collaborationHubs.viewMembers)
                  }
                </span>
              </Button>
            )}
          </div>
        </div>

        <div className="flex items-center justify-between flex-wrap gap-4">
          <div className="text-sm text-muted-foreground">
            {hub.createdAt && (
              <span>
                {t(keys.collaborationHubs.created)} {formatDistanceToNow(new Date(hub.createdAt), { addSuffix: true })}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Manage Participants Dialog */}
      <ManageParticipantsDialog
        open={manageParticipantsOpen}
        onOpenChange={setManageParticipantsOpen}
        hub={hub}
      />
    </div>
  )
}
