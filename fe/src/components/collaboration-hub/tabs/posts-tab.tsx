import { useState } from "react"
import { Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { PostFormDialog, PostsList } from "@/components/posts"
import { usePermissions } from "@/hooks/use-permissions"
import { toast } from "sonner"

interface PostsTabProps {
  hubId: number
}

export function PostsTab({ hubId }: PostsTabProps) {
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [editingPostId, setEditingPostId] = useState<number | null>(null)
  const { t, keys } = useTranslations()
  const { canCreatePost } = usePermissions()





  const handleCreatePost = () => {
    if (!canCreatePost()) {
      toast.error("You don't have permission to create posts")
      return
    }
    setCreateDialogOpen(true)
  }

  const handleEditPost = (postId: number) => {
    setEditingPostId(postId)
    setEditDialogOpen(true)
  }

  const handleDialogSuccess = () => {
    // Show success message
    if (editingPostId) {
      toast.success(t(keys.collaborationHubs.posts.postUpdated))
    } else {
      toast.success(t(keys.collaborationHubs.posts.postCreated))
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header with create action */}
      <div className="flex justify-end mb-6">
        {canCreatePost() && (
          <Button onClick={handleCreatePost}>
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">{t(keys.collaborationHubs.posts.createPost)}</span>
          </Button>
        )}
      </div>

      {/* Posts Content */}
      <PostsList
        hubId={hubId}
        onEditPost={handleEditPost}
        className="flex-1"
        showFilters={true}
      />

      {/* Create Post Dialog */}
      <PostFormDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        hubId={hubId}
        onSuccess={handleDialogSuccess}
      />

      {/* Edit Post Dialog */}
      <PostFormDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        hubId={hubId}
        postId={editingPostId}
        onSuccess={handleDialogSuccess}
      />
    </div>
  )
}
