import { useState } from "react"
import { Send } from "lucide-react"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { TextareaWithEmoji } from "@/components/ui/textarea-with-emoji"
import { MentionInput } from "@/components/ui/mention-input"
import { useCreateComment } from "@/hooks/comments"
import { useAuth } from "@/contexts/auth-context"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { toast } from "sonner"

interface CommentFormProps {
  postId: number
  hubId: number
  onCommentCreated?: () => void
  className?: string
}

export function CommentForm({ postId, hubId, onCommentCreated, className }: CommentFormProps) {
  const [content, setContent] = useState("")
  const { user } = useAuth()
  const { t, keys } = useTranslations()
  const createComment = useCreateComment()

  const getInitials = (name?: string) => {
    if (!name) return '??'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!content.trim()) return

    try {
      await createComment.mutateAsync({
        params: { path: { postId } },
        body: { content: content.trim() }
      })
      
      setContent("")
      onCommentCreated?.()
      toast.success(t(keys.collaborationHubs.posts.comments.commentPosted))
    } catch (__error) {
      // Error handling for comment creation
      toast.error(t(keys.collaborationHubs.posts.comments.failedToPost))
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  return (
    <form onSubmit={handleSubmit} className={`flex gap-3 ${className}`}>
      {/* User Avatar */}
      <Avatar className="h-8 w-8 flex-shrink-0 mt-1">
        <AvatarFallback className="text-xs">
          {getInitials(user?.display_name)}
        </AvatarFallback>
      </Avatar>

      {/* Comment Input Row */}
      <div className="flex-1 flex gap-2 items-start">
        <div className="flex-1">
          <MentionInput
            hubId={hubId}
            value={content}
            onChange={setContent}
            onKeyDown={handleKeyDown}
            placeholder={t(keys.collaborationHubs.posts.comments.writeComment)}
            disabled={createComment.isPending}
            className="flex-1"
          >
            {({ ref, value, displayValue, onChange, onKeyDown, onSelect, placeholder, disabled }) => (
              <TextareaWithEmoji
                ref={ref as React.RefObject<HTMLTextAreaElement>}
                value={displayValue || value}
                onChange={(newValue) => onChange({ target: { value: newValue } } as React.ChangeEvent<HTMLTextAreaElement>)}
                onKeyDown={onKeyDown}
                onSelect={onSelect}
                placeholder={placeholder}
                className="min-h-[40px] text-sm resize-none"
                disabled={disabled}
                rows={1}
              />
            )}
          </MentionInput>
          <div className="text-xs text-muted-foreground mt-1">
            {t(keys.collaborationHubs.posts.comments.ctrlEnterToPost)}
          </div>
        </div>

        <Button
          type="submit"
          size="sm"
          disabled={createComment.isPending || !content.trim()}
          className="gap-1 mt-0.5"
        >
          {createComment.isPending ? (
            t(keys.collaborationHubs.posts.comments.posting)
          ) : (
            <>
              <Send className="h-3 w-3" />
              {t(keys.collaborationHubs.posts.comments.postComment)}
            </>
          )}
        </Button>
      </div>
    </form>
  )
}
