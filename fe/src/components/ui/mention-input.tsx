import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react'
import { User, Crown, Eye, Camera } from 'lucide-react'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import { useHubParticipants } from '@/hooks/collaboration-hubs'
import { useTranslations } from '@/lib/i18n/typed-translations'
import type { HubParticipantResponse } from '@/components/collaboration-hub/types'

interface MentionInputProps {
  hubId: number
  value: string
  onChange: (value: string) => void
  onKeyDown?: (e: React.KeyboardEvent) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  children: (props: {
    ref: React.RefObject<HTMLInputElement | HTMLTextAreaElement>
    value: string
    displayValue?: string // Visual representation with formatted mentions
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
    onKeyDown: (e: React.KeyboardEvent) => void
    onSelect: (e: React.SyntheticEvent) => void
    placeholder?: string
    disabled?: boolean
    className?: string
  }) => React.ReactNode
}

interface MentionMatch {
  start: number
  end: number
  query: string
}

// Stores the actual mention data and positions for visual formatting
interface FormattedMention {
  participant: HubParticipantResponse
  start: number
  end: number
  displayText: string
  rawText: string // The email prefix version for backend
}

export function MentionInput({
                               hubId,
                               value,
                               onChange,
                               onKeyDown,
                               placeholder,
                               disabled,
                               className,
                               children
                             }: MentionInputProps) {
  const { t, keys } = useTranslations()
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [mentionMatch, setMentionMatch] = useState<MentionMatch | null>(null)
  const [formattedMentions, setFormattedMentions] = useState<FormattedMention[]>([])
  const [displayValue, setDisplayValue] = useState<string>(value)
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null)
  const popoverRef = useRef<HTMLDivElement>(null)

  const { data: participantsData, isLoading } = useHubParticipants(hubId, {
    enabled: !!hubId && !disabled,
    size: 100
  })

  const filteredParticipants = useMemo(() => {
    if (!mentionMatch) return []

    const participants = participantsData?.content || []
    return participants.filter(p => {
      const query = mentionMatch.query.toLowerCase()
      const emailPrefix = p.email?.split('@')[0]?.toLowerCase() || ''
      const name = p.name?.toLowerCase() || ''

      // Prioritize exact email prefix matches
      return emailPrefix.includes(query) || name.includes(query)
    }).sort((a, b) => {
      const query = mentionMatch.query.toLowerCase()
      const aEmailPrefix = a.email?.split('@')[0]?.toLowerCase() || ''
      const bEmailPrefix = b.email?.split('@')[0]?.toLowerCase() || ''

      // Prioritize exact email prefix matches first
      const aEmailMatch = aEmailPrefix.startsWith(query)
      const bEmailMatch = bEmailPrefix.startsWith(query)

      if (aEmailMatch && !bEmailMatch) return -1
      if (!aEmailMatch && bEmailMatch) return 1

      // Then prioritize email prefix contains over name matches
      const aEmailContains = aEmailPrefix.includes(query)
      const bEmailContains = bEmailPrefix.includes(query)

      if (aEmailContains && !bEmailContains) return -1
      if (!aEmailContains && bEmailContains) return 1

      return 0
    })
  }, [mentionMatch, participantsData?.content])

  // Handle click outside to close popup
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpen &&
          popoverRef.current &&
          !popoverRef.current.contains(event.target as Node) &&
          inputRef.current &&
          !inputRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setMentionMatch(null)
        setSelectedIndex(0)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const getInitials = (name?: string) => {
    if (!name) return '?'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="h-3 w-3 text-yellow-600" />
      case 'reviewer':
        return <Eye className="h-3 w-3 text-blue-600" />
      case 'reviewer_creator':
        return <Camera className="h-3 w-3 text-purple-600" />
      case 'content_creator':
        return <User className="h-3 w-3 text-green-600" />
      default:
        return <User className="h-3 w-3 text-gray-600" />
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return t(keys.collaborationHubs.roles.admin)
      case 'reviewer':
        return t(keys.collaborationHubs.roles.reviewer)
      case 'reviewer_creator':
        return t(keys.collaborationHubs.roles.reviewer_creator)
      case 'content_creator':
        return t(keys.collaborationHubs.roles.content_creator)
      default:
        return role
    }
  }

  const findMentionMatch = useCallback((text: string, cursor: number): MentionMatch | null => {
    const before = text.slice(0, cursor)
    // Match @ followed by any characters (for name-based search in dropdown)
    // This allows users to type names while we'll insert emails behind the scenes
    const match = before.match(/@([a-zA-Z0-9._-]*)$/)
    return match
      ? { start: match.index!, end: cursor, query: match[1] }
      : null
  }, [])

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = e.target.value
    const cursor = e.target.selectionStart ?? 0
    const oldValue = value

    onChange(newValue)

    // Check if space was typed after a mention
    if (newValue.length > oldValue.length && newValue[cursor - 1] === ' ') {
      const beforeSpace = newValue.slice(0, cursor - 1)
      // Check for email patterns
      const spaceMatch = beforeSpace.match(/@[a-zA-Z0-9._-]+$/)
      if (spaceMatch && isOpen) {
        // Space was typed after a mention, close the popup
        setIsOpen(false)
        setMentionMatch(null)
        setSelectedIndex(0)
        return
      }
    }

    const match = findMentionMatch(newValue, cursor)
    setMentionMatch(match)
    setIsOpen(!!match)
    setSelectedIndex(0)
  }, [onChange, findMentionMatch, value, isOpen])

  const insertMention = useCallback((participant: HubParticipantResponse) => {
    if (!mentionMatch || !inputRef.current) return

    const before = value.slice(0, mentionMatch.start)
    const after = value.slice(mentionMatch.end)

    // Insert participant name for display
    const participantName = participant.name || participant.email?.split('@')[0] || 'unknown'
    const mentionText = `@${participantName} `
    const newValue = before + mentionText + after

    // Store the mention data for later conversion to email prefix
    const newMention: FormattedMention = {
      participant,
      start: mentionMatch.start,
      end: mentionMatch.start + mentionText.length - 1, // Don't include the space
      displayText: mentionText,
      rawText: `@${participant.email?.split('@')[0] || 'unknown'} `
    }

    // Update the list of formatted mentions
    const updatedMentions = [...formattedMentions, newMention]
    setFormattedMentions(updatedMentions)

    // Update the value with participant name for display
    onChange(newValue)
    setDisplayValue(newValue)

    const newCursor = mentionMatch.start + mentionText.length
    setTimeout(() => {
      inputRef.current?.setSelectionRange(newCursor, newCursor)
      inputRef.current?.focus()
    }, 0)

    setIsOpen(false)
    setMentionMatch(null)
    setSelectedIndex(0)
  }, [mentionMatch, value, onChange, formattedMentions])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (isOpen && filteredParticipants.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev => (prev + 1) % filteredParticipants.length)
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev => (prev - 1 + filteredParticipants.length) % filteredParticipants.length)
          break
        case 'Enter':
        case 'Tab':
          e.preventDefault()
          insertMention(filteredParticipants[selectedIndex])
          break
        case 'Escape':
          e.preventDefault()
          setIsOpen(false)
          setMentionMatch(null)
          setSelectedIndex(0)
          break
        case ' ':
          // Space key - close the popup if we're at the end of a mention
          if (mentionMatch) {
            setIsOpen(false)
            setMentionMatch(null)
            setSelectedIndex(0)
          }
          onKeyDown?.(e)
          break
        default:
          onKeyDown?.(e)
      }
    } else {
      onKeyDown?.(e)
    }
  }, [isOpen, filteredParticipants, selectedIndex, mentionMatch, onKeyDown, insertMention])


  const handleParticipantSelect = useCallback((e: React.MouseEvent, p: HubParticipantResponse) => {
    e.preventDefault()
    e.stopPropagation()
    insertMention(p)
  }, [insertMention])

  const handleSelect = useCallback(() => {
    // Don't interfere with mention selection when popup is open
    if (isOpen) {
      return
    }

    // Only update the mention match, don't close the popup aggressively
    setTimeout(() => {
      const cursor = inputRef.current?.selectionStart ?? 0
      const match = findMentionMatch(value, cursor)

      // Only close if we're clearly outside a mention context
      if (!match && mentionMatch) {
        // Check if cursor moved outside the mention range
        const mentionEnd = mentionMatch.start + mentionMatch.query.length + 1 // +1 for @
        if (cursor < mentionMatch.start || cursor > mentionEnd + 2) { // +2 for some tolerance
          setIsOpen(false)
          setMentionMatch(null)
          setSelectedIndex(0)
        }
      } else if (match) {
        setMentionMatch(match)
        if (!isOpen) {
          setIsOpen(true)
          setSelectedIndex(0)
        }
      }
    }, 0)
  }, [value, findMentionMatch, mentionMatch, isOpen])

  // Convert display value (with names) to raw value (with email prefixes) for backend
  const convertToRawValue = useCallback((displayVal: string) => {
    if (!formattedMentions.length) return displayVal

    let result = displayVal
    // Sort mentions by position (reverse order to maintain positions)
    const sortedMentions = [...formattedMentions].sort((a, b) => b.start - a.start)

    sortedMentions.forEach(mention => {
      const displayMention = `@${mention.participant.name}`
      const rawMention = mention.rawText.trim()
      result = result.replace(displayMention, rawMention)
    })

    return result
  }, [formattedMentions])

  // Update display value when value changes from outside
  useEffect(() => {
    setDisplayValue(value)
  }, [value])

  return (
    <div className={cn("relative", className)}>
      {children({
        ref: inputRef,
        value: convertToRawValue(displayValue), // Send raw value to backend
        displayValue: displayValue, // Show display value to user
        onChange: handleInputChange,
        onKeyDown: handleKeyDown,
        onSelect: handleSelect,
        placeholder,
        disabled,
        className
      })}



      {isOpen && (
        <div
          ref={popoverRef}
          className="absolute z-50 w-80 bottom-full mb-1 bg-popover border border-border rounded-md shadow-md"
        >
          <div className="p-2">
            {isLoading ? (
              <div className="text-center py-4 text-sm text-muted-foreground">
                {t(keys.ui.mentionInput.loadingParticipants)}
              </div>
            ) : filteredParticipants.length === 0 ? (
              <div className="text-center py-4 text-sm text-muted-foreground">
                {t(keys.ui.mentionInput.noParticipantsFound)}
              </div>
            ) : (
              <ScrollArea className="max-h-64">
                {filteredParticipants.map((p, index) => (
                  <div
                    key={p.id}
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleParticipantSelect(e, p)
                    }}
                    className={cn(
                      "flex items-center gap-3 p-3 cursor-pointer rounded-md hover:bg-accent",
                      index === selectedIndex && "bg-accent"
                    )}
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="text-xs">
                        {getInitials(p.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <p className="font-medium text-sm truncate">
                          {p.name || 'Unknown'}
                        </p>
                        {p.role && <span>{getRoleIcon(p.role)}</span>}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <p className="text-xs text-muted-foreground truncate">
                          {p.email}
                        </p>
                        {p.role && (
                          <Badge variant="secondary" className="text-xs px-1 py-0">
                            {getRoleLabel(p.role)}
                          </Badge>
                        )}
                        {p.isExternal && (
                          <Badge variant="outline" className="text-xs px-1 py-0">
                            External
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </ScrollArea>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
