import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Send, Paperclip, Smile, X, Upload, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { MentionInput } from '@/components/ui/mention-input';

import { useTranslations } from '@/lib/i18n/typed-translations';
import { useWebSocket } from '@/lib/websocket';
import { useSendMessage } from '@/hooks/chat';
import { useUploadChatAttachment } from '@/hooks/chat';
import { cn } from '@/lib/utils';

interface MessageInputProps {
  channelId: number;
  hubId: number;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  // Editing mode props
  isEditing?: boolean;
  editingContent?: string;
  onSaveEdit?: (content: string) => void;
  onCancelEdit?: () => void;
}

interface AttachmentPreview {
  file: File;
  url: string;
  uploading: boolean;
  uploadedUrl?: string;
  uploadedMetadata?: {
    url: string;
    filename: string;
    size: number;
    content_type: string;
    type: string;
  };
  error?: string;
}

export function MessageInput({
  channelId,
  hubId,
  placeholder,
  disabled,
  className,
  isEditing = false,
  editingContent = '',
  onSaveEdit,
  onCancelEdit
}: MessageInputProps) {
  const { t, keys } = useTranslations();
  const { sendMessage: sendWebSocketMessage, sendTypingIndicator, isConnected } = useWebSocket();
  const sendMessageMutation = useSendMessage();
  const { uploadFile, isLoading: isUploading, progress } = useUploadChatAttachment();
  
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<AttachmentPreview[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Initialize message content for editing mode and focus input
  useEffect(() => {
    if (isEditing && editingContent) {
      setMessage(editingContent);
      // Focus the input when editing starts
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          // Move cursor to end of text
          inputRef.current.setSelectionRange(editingContent.length, editingContent.length);
        }
      }, 0);
    } else if (!isEditing) {
      setMessage('');
    }
  }, [isEditing, editingContent]);

  // Handle typing indicators
  const handleTyping = useCallback(() => {
    if (!isTyping && isConnected) {
      setIsTyping(true);
      sendTypingIndicator(channelId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 2000);
  }, [isTyping, isConnected, sendTypingIndicator, channelId]);

  // Cleanup typing timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  const handleFileSelect = useCallback(async (files: FileList) => {
    const newAttachments: AttachmentPreview[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const preview: AttachmentPreview = {
        file,
        url: URL.createObjectURL(file),
        uploading: true,
      };
      newAttachments.push(preview);
    }

    setAttachments(prev => [...prev, ...newAttachments]);

    // Upload files
    for (let i = 0; i < newAttachments.length; i++) {
      const attachment = newAttachments[i];
      try {
        const result = await uploadFile(attachment.file);
        setAttachments(prev => prev.map(a =>
          a.file === attachment.file
            ? {
                ...a,
                uploading: false,
                uploadedUrl: result.url,
                uploadedMetadata: {
                  url: result.url,
                  filename: result.filename,
                  size: result.size,
                  content_type: result.mimeType,
                  type: result.type
                }
              }
            : a
        ));
      } catch (error) {
        setAttachments(prev => prev.map(a =>
          a.file === attachment.file
            ? { ...a, uploading: false, error: error instanceof Error ? error.message : 'Upload failed' }
            : a
        ));
      }
    }
  }, [uploadFile]);

  const handleRemoveAttachment = useCallback((index: number) => {
    setAttachments(prev => {
      const attachment = prev[index];
      URL.revokeObjectURL(attachment.url);
      return prev.filter((_, i) => i !== index);
    });
  }, []);

  const handleSendMessage = useCallback(async () => {
    if (!message.trim() && attachments.length === 0) return;
    if (disabled || sendMessageMutation.isPending) return;

    // Check if any attachments are still uploading
    const uploadingAttachments = attachments.filter(a => a.uploading);
    if (uploadingAttachments.length > 0) {
      return; // Wait for uploads to complete
    }

    // Check if any attachments failed to upload
    const failedAttachments = attachments.filter(a => a.error);
    if (failedAttachments.length > 0) {
      return; // Don't send if there are failed uploads
    }

    const attachmentUris = attachments
      .filter(a => a.uploadedUrl)
      .map(a => a.uploadedUrl!);

    const attachmentMetadata = attachments
      .filter(a => a.uploadedMetadata)
      .map(a => a.uploadedMetadata!);

    try {
      // Use REST API when attachments are present to send complete metadata
      // WebSocket only supports attachment URIs, not full metadata
      if (isConnected && attachmentMetadata.length === 0) {
        // Send via WebSocket for real-time delivery (text-only messages)
        sendWebSocketMessage(channelId, message.trim(), attachmentUris);
      } else {
        // Use REST API for messages with attachments or when WebSocket is not connected
        await sendMessageMutation.mutateAsync({
          params: { path: { channelId } },
          body: {
            content: message.trim() || "", // Ensure content is never null
            attachment_uris: attachmentUris, // Simple URIs for backward compatibility
            // Note: Enhanced attachment metadata is handled by the backend
          },
        });
      }

      // Clear input and attachments
      setMessage('');
      setAttachments(prev => {
        prev.forEach(a => URL.revokeObjectURL(a.url));
        return [];
      });
      setIsTyping(false);
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    } catch (__error) {
      // Error handling for message sending
    }
  }, [message, attachments, disabled, sendMessageMutation, isConnected, sendWebSocketMessage, channelId]);

  const handleSaveEdit = useCallback(() => {
    if (!message.trim() || !onSaveEdit) return;
    onSaveEdit(message.trim());
  }, [message, onSaveEdit]);

  const handleCancelEdit = useCallback(() => {
    if (onCancelEdit) {
      onCancelEdit();
    }
  }, [onCancelEdit]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (isEditing) {
        handleSaveEdit();
      } else {
        handleSendMessage();
      }
    } else if (e.key === 'Escape' && isEditing) {
      e.preventDefault();
      handleCancelEdit();
    }
  }, [handleSendMessage, handleSaveEdit, handleCancelEdit, isEditing]);

  const handleMessageChange = useCallback((newMessage: string) => {
    setMessage(newMessage);
    handleTyping();
  }, [handleTyping]);

  const canSend = (message.trim() || attachments.some(a => a.uploadedUrl)) &&
                 !sendMessageMutation.isPending &&
                 !attachments.some(a => a.uploading) &&
                 !disabled;

  const canSaveEdit = message.trim() && isEditing;

  return (
    <div className={cn("space-y-2 w-full", className)}>
      {/* Attachment Previews */}
      {attachments.length > 0 && (
        <div className="flex flex-wrap gap-2 p-2 border rounded-lg bg-muted/30 max-h-32 overflow-y-auto">
          {attachments.map((attachment, index) => (
            <div key={index} className="relative flex items-center gap-2 p-2 border rounded bg-background">
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{attachment.file.name}</p>
                {attachment.uploading && (
                  <div className="space-y-1">
                    <Progress value={progress} className="h-1" />
                    <p className="text-xs text-muted-foreground">{t(keys.collaborationHubs.chat.uploading)}</p>
                  </div>
                )}
                {attachment.error && (
                  <p className="text-xs text-destructive">{attachment.error}</p>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => handleRemoveAttachment(index)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Message Input */}
      <div className="relative w-full">
        <div className={cn(
          "flex items-center gap-2 px-4 py-3 border rounded-lg bg-background w-full min-w-0",
          isEditing && "border-blue-300 dark:border-blue-600"
        )}>
          <MentionInput
            hubId={hubId}
            value={message}
            onChange={handleMessageChange}
            onKeyDown={handleKeyPress}
            placeholder={
              isEditing
                ? "Edit your message..."
                : placeholder || t(keys.collaborationHubs.chat.messagePlaceholder, { channelName: 'channel' })
            }
            disabled={disabled}
            className="flex-1 min-w-0"
          >
            {({ ref, value, displayValue, onChange, onKeyDown, onSelect, placeholder, disabled }) => {
              // Use callback ref pattern to handle both refs properly
              const combinedRef = (el: HTMLInputElement | null) => {
                // Set the MentionInput's ref if it exists and is mutable
                if (ref && 'current' in ref && ref.current !== undefined) {
                  try {
                    (ref as React.MutableRefObject<HTMLInputElement | null>).current = el;
                  } catch (__error) {
                    // Ignore read-only ref errors
                  }
                }
                // Set our local ref for editing focus - use a different approach
                if (inputRef && 'current' in inputRef) {
                  try {
                    (inputRef as React.MutableRefObject<HTMLInputElement | null>).current = el;
                  } catch (__error) {
                    // Ignore read-only ref errors
                  }
                }
              };

              return (
                <Input
                  ref={combinedRef}
                  placeholder={placeholder}
                  value={displayValue || value}
                  onChange={onChange}
                  onKeyDown={onKeyDown}
                  onSelect={onSelect}
                  disabled={disabled}
                  className={cn(
                    "border-0 focus-visible:ring-0 px-0 py-0 h-auto bg-transparent dark:bg-transparent placeholder:text-muted-foreground shadow-none",
                    isEditing && "placeholder:text-blue-500/70 dark:placeholder:text-blue-400/70"
                  )}
                />
              );
            }}
          </MentionInput>

          {/* File Upload Button - Hide during editing */}
          {!isEditing && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 shrink-0 hover:bg-muted"
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled || isUploading}
              title={t(keys.collaborationHubs.chat.attachFile)}
            >
              {isUploading ? <Upload className="h-4 w-4 animate-spin" /> : <Paperclip className="h-4 w-4" />}
            </Button>
          )}

          {/* Emoji Button - Hide during editing */}
          {!isEditing && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 shrink-0 hover:bg-muted"
              disabled={disabled}
              title={t(keys.collaborationHubs.chat.addEmoji)}
            >
              <Smile className="h-4 w-4" />
            </Button>
          )}

          {/* Editing Mode Buttons */}
          {isEditing ? (
            <>
              {/* Subtle editing indicator */}
              <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse shrink-0" title="Editing message" />

              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 shrink-0 bg-white hover:bg-gray-50 text-gray-500 hover:text-gray-700 border border-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-400 dark:hover:text-gray-200 dark:border-gray-600"
                onClick={handleCancelEdit}
                title="Cancel editing (Esc)"
              >
                <X className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 shrink-0 bg-white hover:bg-green-50 text-green-600 hover:text-green-700 border border-green-200 dark:bg-green-900/20 dark:hover:bg-green-900/30 dark:text-green-400 dark:hover:text-green-300 dark:border-green-800"
                onClick={handleSaveEdit}
                disabled={!canSaveEdit}
                title="Save changes (Enter)"
              >
                <Check className="h-4 w-4" />
              </Button>
            </>
          ) : (
            /* Send Button */
            <Button
              onClick={handleSendMessage}
              disabled={!canSend}
              className="h-8 w-8 p-0 shrink-0 bg-white hover:bg-blue-50 text-blue-600 hover:text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 dark:text-blue-400 dark:hover:text-blue-300 dark:border-blue-800"
              size="sm"
              title={t(keys.collaborationHubs.chat.send)}
            >
              <Send className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*,video/*,.pdf,.doc,.docx,.txt"
        onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
        className="hidden"
      />
    </div>
  );
}
