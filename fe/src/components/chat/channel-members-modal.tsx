import { Users, Crown, Shield, User, Circle } from 'lucide-react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useChannelMembers } from '@/hooks/chat/use-channel-members';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import type { HubParticipantResponse } from '@/lib/types/api';

interface ChannelMembersModalProps {
  hubId: number;
  channelId: number;
  channelName: string;
  isOpen: boolean;
  onClose: () => void;
}

export function ChannelMembersModal({ 
  hubId, 
  channelId, 
  channelName, 
  isOpen, 
  onClose 
}: ChannelMembersModalProps) {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();
  
  const { data: membersResponse, isLoading, error } = useChannelMembers(
    hubId, 
    channelId, 
    { enabled: isOpen }
  );
  
  const members = membersResponse?.content || [];

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return <Crown className="h-4 w-4 text-yellow-600" />;
      case 'reviewer':
        return <Shield className="h-4 w-4 text-blue-600" />;
      case 'content_creator':
        return <User className="h-4 w-4 text-green-600" />;
      default:
        return <User className="h-4 w-4 text-gray-600" />;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
      case 'reviewer':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'content_creator':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };

  const getOnlineStatus = (isOnline?: boolean) => {
    if (isOnline === undefined) return null;
    
    return (
      <div className="flex items-center gap-1">
        <Circle 
          className={cn(
            "h-2 w-2 fill-current",
            isOnline ? "text-green-500" : "text-gray-400"
          )} 
        />
        <span className="text-xs text-muted-foreground">
          {isOnline ? 'Online' : 'Offline'}
        </span>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn(
        "max-w-md",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0"
      )}>
        <DialogHeader className={cn(
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <DialogTitle className={cn(
            "flex items-center gap-2",
            isMobile && "text-base"
          )}>
            <Users className={cn("h-5 w-5", isMobile && "h-4 w-4")} />
            {channelName} {t(keys.collaborationHubs.chat.members)}
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className={cn(
          "max-h-96",
          isMobile && "h-[calc(100dvh-80px)] px-4"
        )}>
          {isLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="flex items-center gap-3 p-2">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="flex-1 space-y-1">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                  <Skeleton className="h-5 w-16" />
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-sm text-muted-foreground">
                {t(keys.collaborationHubs.chat.failedToLoadMembers)}
              </p>
            </div>
          ) : members.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-sm text-muted-foreground">
                {t(keys.collaborationHubs.chat.noMembers)}
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              {members.map((member: HubParticipantResponse) => (
                <div 
                  key={member.id} 
                  className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="text-sm font-medium">
                      {getInitials(member.name || '')}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      {getRoleIcon(member.role)}
                      <p className="font-medium text-sm truncate">
                        {member.name}
                      </p>
                      {member.isExternal && (
                        <Badge variant="outline" className="text-xs">
                          External
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 mt-1">
                      <p className="text-xs text-muted-foreground truncate">
                        {member.email}
                      </p>
                      {getOnlineStatus(false)} {/* Online status not available in hub participants */}
                    </div>
                  </div>
                  
                  <Badge className={getRoleBadgeColor(member.role)}>
                    {member.role.replace('_', ' ')}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>

        {!isLoading && !error && members.length > 0 && (
          <div className="pt-2 border-t">
            <p className="text-sm text-muted-foreground text-center">
              {members.length} {members.length === 1 ? 'member' : 'members'}
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
