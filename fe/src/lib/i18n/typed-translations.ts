import { useTranslation } from 'react-i18next';
import type { TranslationResources } from './types';

/**
 * Type-safe translation system with object-based access pattern.
 *
 * New Usage Pattern:
 * const { t, keys } = useTypedTranslations();
 * const title = t(keys.brands.title); // Type-safe access
 * const count = t(keys.brands.count, { count: 1 }); // With parameters
 */

// Helper type to create nested objects that represent translation key paths
type DeepTranslationKeys<T> = {
  readonly [K in keyof T]: T[K] extends string
    ? string
    : T[K] extends Record<string, unknown>
    ? DeepTranslationKeys<T[K]>
    : string;
};

// Type for our translation keys object
export type TypedTranslationKeys = DeepTranslationKeys<TranslationResources>;

// Legacy type for backward compatibility
export type TypedTranslations = DeepTranslationKeys<TranslationResources>;

// Pre-built translation keys object
const translationKeys: TypedTranslationKeys = {
  auth: {
    login: {
      title: 'auth.login.title',
      description: 'auth.login.description',
      email: 'auth.login.email',
      password: 'auth.login.password',
      forgotPassword: 'auth.login.forgotPassword',
      submit: 'auth.login.submit',
      submitting: 'auth.login.submitting',
      error: 'auth.login.error',
      noAccount: 'auth.login.noAccount',
      signUp: 'auth.login.signUp'
    },
    register: {
      title: 'auth.register.title',
      description: 'auth.register.description',
      accountName: 'auth.register.accountName',
      displayName: 'auth.register.displayName',
      email: 'auth.register.email',
      password: 'auth.register.password',
      passwordHint: 'auth.register.passwordHint',
      submit: 'auth.register.submit',
      submitting: 'auth.register.submitting',
      error: 'auth.register.error',
      hasAccount: 'auth.register.hasAccount',
      signIn: 'auth.register.signIn',
      success: {
        title: 'auth.register.success.title',
        description: 'auth.register.success.description',
        checkEmail: 'auth.register.success.checkEmail',
        registerAnother: 'auth.register.success.registerAnother',
        goToLogin: 'auth.register.success.goToLogin'
      }
    },
    verifying: 'auth.verifying',
    initializing: 'auth.initializing'
  },
  accountCompanies: {
    title: 'accountCompanies.title',
    description: 'accountCompanies.description',
    createNew: 'accountCompanies.createNew',
    noCompanies: 'accountCompanies.noCompanies',
    noCompaniesDescription: 'accountCompanies.noCompaniesDescription',
    loading: 'accountCompanies.loading',
    error: 'accountCompanies.error',
    edit: 'accountCompanies.edit',
    delete: 'accountCompanies.delete',
    confirmDelete: 'accountCompanies.confirmDelete',
    deleteDescription: 'accountCompanies.deleteDescription',
    deleting: 'accountCompanies.deleting',
    creating: 'accountCompanies.creating',
    updating: 'accountCompanies.updating',
    createTitle: 'accountCompanies.createTitle',
    editTitle: 'accountCompanies.editTitle',
    save: 'accountCompanies.save',
    cancel: 'accountCompanies.cancel',
    companyName: 'accountCompanies.companyName',
    email: 'accountCompanies.email',
    phone: 'accountCompanies.phone',
    website: 'accountCompanies.website',
    addressStreet: 'accountCompanies.addressStreet',
    addressCity: 'accountCompanies.addressCity',
    addressPostalCode: 'accountCompanies.addressPostalCode',
    addressCountry: 'accountCompanies.addressCountry',
    vatNumber: 'accountCompanies.vatNumber',
    registrationNumber: 'accountCompanies.registrationNumber',
    required: 'accountCompanies.required',
    validation: {
      companyNameRequired: 'accountCompanies.validation.companyNameRequired',
      companyNameTooLong: 'accountCompanies.validation.companyNameTooLong',
      emailInvalid: 'accountCompanies.validation.emailInvalid',
      emailTooLong: 'accountCompanies.validation.emailTooLong',
      phoneTooLong: 'accountCompanies.validation.phoneTooLong',
      websiteTooLong: 'accountCompanies.validation.websiteTooLong',
      addressStreetTooLong: 'accountCompanies.validation.addressStreetTooLong',
      addressCityTooLong: 'accountCompanies.validation.addressCityTooLong',
      addressPostalCodeTooLong: 'accountCompanies.validation.addressPostalCodeTooLong',
      addressCountryTooLong: 'accountCompanies.validation.addressCountryTooLong',
      vatNumberTooLong: 'accountCompanies.validation.vatNumberTooLong',
      registrationNumberTooLong: 'accountCompanies.validation.registrationNumberTooLong'
    }
  },
  bankDetails: {
    title: 'bankDetails.title',
    description: 'bankDetails.description',
    createNew: 'bankDetails.createNew',
    noBankDetails: 'bankDetails.noBankDetails',
    noBankDetailsDescription: 'bankDetails.noBankDetailsDescription',
    loading: 'bankDetails.loading',
    error: 'bankDetails.error',
    edit: 'bankDetails.edit',
    delete: 'bankDetails.delete',
    confirmDelete: 'bankDetails.confirmDelete',
    deleteDescription: 'bankDetails.deleteDescription',
    deleting: 'bankDetails.deleting',
    creating: 'bankDetails.creating',
    updating: 'bankDetails.updating',
    createTitle: 'bankDetails.createTitle',
    editTitle: 'bankDetails.editTitle',
    save: 'bankDetails.save',
    cancel: 'bankDetails.cancel',
    name: 'bankDetails.name',
    bankName: 'bankDetails.bankName',
    iban: 'bankDetails.iban',
    bicSwift: 'bankDetails.bicSwift',
    required: 'bankDetails.required',
    validation: {
      nameRequired: 'bankDetails.validation.nameRequired',
      nameTooLong: 'bankDetails.validation.nameTooLong',
      bankNameTooLong: 'bankDetails.validation.bankNameTooLong',
      ibanInvalid: 'bankDetails.validation.ibanInvalid',
      ibanTooShort: 'bankDetails.validation.ibanTooShort',
      ibanTooLong: 'bankDetails.validation.ibanTooLong',
      bicSwiftInvalid: 'bankDetails.validation.bicSwiftInvalid',
      bicSwiftTooShort: 'bankDetails.validation.bicSwiftTooShort',
      bicSwiftTooLong: 'bankDetails.validation.bicSwiftTooLong'
    }
  },
  invoices: {
    title: 'invoices.title',
    description: 'invoices.description',
    createNew: 'invoices.createNew',
    loading: 'invoices.loading',
    error: 'invoices.error',
    noInvoices: 'invoices.noInvoices',
    noInvoicesDescription: 'invoices.noInvoicesDescription',
    daysOverdue: 'invoices.daysOverdue',
    dueIn: 'invoices.dueIn',
    dueToday: 'invoices.dueToday',
    invoiceNumber: 'invoices.invoiceNumber',
    recipient: 'invoices.recipient',
    issueDate: 'invoices.issueDate',
    dueDate: 'invoices.dueDate',
    viewDetails: 'invoices.viewDetails',
    edit: 'invoices.edit',
    send: 'invoices.send',
    download: 'invoices.download',
    delete: 'invoices.delete',
    cancel: 'invoices.cancel',
    primary: 'invoices.primary',
    cc: 'invoices.cc',
    sending: 'invoices.sending',
    deleting: 'invoices.deleting',
    subtotal: 'invoices.subtotal',
    totalVat: 'invoices.totalVat',
    totalAmount: 'invoices.totalAmount',
    confirmDelete: 'invoices.confirmDelete',
    deleteDescription: 'invoices.deleteDescription',
    page: 'invoices.page',
    of: 'invoices.of',
    previous: 'invoices.previous',
    next: 'invoices.next',
    filters: {
      title: 'invoices.filters.title',
      status: 'invoices.filters.status',
      dateRange: 'invoices.filters.dateRange',
      fromDate: 'invoices.filters.fromDate',
      toDate: 'invoices.filters.toDate',
      clearAll: 'invoices.filters.clearAll',
      apply: 'invoices.filters.apply',
      activeFilters: 'invoices.filters.activeFilters'
    },
    status: {
      draft: 'invoices.status.draft',
      sent: 'invoices.status.sent',
      paid: 'invoices.status.paid',
      overdue: 'invoices.status.overdue'
    },
    statusUpdate: {
      title: 'invoices.statusUpdate.title',
      currentStatus: 'invoices.statusUpdate.currentStatus',
      newStatus: 'invoices.statusUpdate.newStatus',
      note: 'invoices.statusUpdate.note',
      update: 'invoices.statusUpdate.update',
      updating: 'invoices.statusUpdate.updating'
    },
    sendDialog: {
      title: 'invoices.sendDialog.title',
      description: 'invoices.sendDialog.description',
      recipientsList: 'invoices.sendDialog.recipientsList',
      confirmSend: 'invoices.sendDialog.confirmSend',
      success: 'invoices.sendDialog.success',
      error: 'invoices.sendDialog.error'
    },
    sendConfirmation: {
      confirmSendTitle: 'invoices.sendConfirmation.confirmSendTitle',
      confirmSendDescription: 'invoices.sendConfirmation.confirmSendDescription',
      confirmResendTitle: 'invoices.sendConfirmation.confirmResendTitle',
      confirmResendDescription: 'invoices.sendConfirmation.confirmResendDescription',
      alreadySentWarning: 'invoices.sendConfirmation.alreadySentWarning',
      send: 'invoices.sendConfirmation.send',
      forceSend: 'invoices.sendConfirmation.forceSend',
      cancel: 'invoices.sendConfirmation.cancel'
    },
    viewDialog: {
      title: 'invoices.viewDialog.title',
      basicInfo: 'invoices.viewDialog.basicInfo',
      lineItems: 'invoices.viewDialog.lineItems',
      noLineItems: 'invoices.viewDialog.noLineItems',
      recipients: 'invoices.viewDialog.recipients',
      totals: 'invoices.viewDialog.totals',
      entityIds: 'invoices.viewDialog.entityIds',
      issuerInfo: 'invoices.viewDialog.issuerInfo',
      recipientInfo: 'invoices.viewDialog.recipientInfo',
      bankDetailsInfo: 'invoices.viewDialog.bankDetailsInfo',
      loading: 'invoices.viewDialog.loading',
      error: 'invoices.viewDialog.error',
      close: 'invoices.viewDialog.close',
      downloadPdf: 'invoices.viewDialog.downloadPdf',
      companyName: 'invoices.viewDialog.companyName',
      name: 'invoices.viewDialog.name',
      address: 'invoices.viewDialog.address',
      contacts: 'invoices.viewDialog.contacts',
      accountName: 'invoices.viewDialog.accountName',
      bankName: 'invoices.viewDialog.bankName',
      vatAmount: 'invoices.viewDialog.vatAmount',
      recipientLabels: {
        type: {
          original: 'invoices.viewDialog.recipientLabels.type.original',
          copy: 'invoices.viewDialog.recipientLabels.type.copy'
        },
        source: {
          brand_contact: 'invoices.viewDialog.recipientLabels.source.brand_contact',
          manual: 'invoices.viewDialog.recipientLabels.source.manual'
        },
        sendCount: 'invoices.viewDialog.recipientLabels.sendCount',
        lastSent: 'invoices.viewDialog.recipientLabels.lastSent',
        noRecipients: 'invoices.viewDialog.recipientLabels.noRecipients'
      }
    },
    form: {
      basicInfo: 'invoices.form.basicInfo',
      clientInfo: 'invoices.form.clientInfo',
      lineItems: 'invoices.form.lineItems',
      recipients: 'invoices.form.recipients',
      totals: 'invoices.form.totals',
      additionalDetails: 'invoices.form.additionalDetails',
      invoiceNumber: 'invoices.form.invoiceNumber',
      generateInvoiceNumber: 'invoices.form.generateInvoiceNumber',
      generatingInvoiceNumber: 'invoices.form.generatingInvoiceNumber',
      currency: 'invoices.form.currency',
      issueDate: 'invoices.form.issueDate',
      dueDate: 'invoices.form.dueDate',
      issuer: 'invoices.form.issuer',
      recipient: 'invoices.form.recipient',
      bankDetails: 'invoices.form.bankDetails',
      myBankDetails: 'invoices.form.myBankDetails',
      notes: 'invoices.form.notes',
      noIssuersFound: 'invoices.form.noIssuersFound',
      noBrandsFound: 'invoices.form.noBrandsFound',
      noBankDetailsFound: 'invoices.form.noBankDetailsFound',
      optional: 'invoices.form.optional',
      description: 'invoices.form.description',
      quantity: 'invoices.form.quantity',
      unitPrice: 'invoices.form.unitPrice',
      vatRate: 'invoices.form.vatRate',
      lineTotal: 'invoices.form.lineTotal',
      lineItem: 'invoices.form.lineItem',
      addItem: 'invoices.form.addItem',
      email: 'invoices.form.email',
      recipientType: 'invoices.form.recipientType',
      addRecipient: 'invoices.form.addRecipient',
      createTitle: 'invoices.form.createTitle',
      editTitle: 'invoices.form.editTitle',
      createInvoice: 'invoices.form.createInvoice',
      updateInvoice: 'invoices.form.updateInvoice',
      creating: 'invoices.form.creating',
      updating: 'invoices.form.updating',
      cancel: 'invoices.form.cancel',
      close: 'invoices.form.close',
      readOnlyMessage: 'invoices.form.readOnlyMessage',
      loadingInvoice: 'invoices.form.loadingInvoice',
      error: 'invoices.form.error',
      placeholders: {
        invoiceNumber: 'invoices.form.placeholders.invoiceNumber',
        selectCurrency: 'invoices.form.placeholders.selectCurrency',
        selectIssuer: 'invoices.form.placeholders.selectIssuer',
        selectRecipient: 'invoices.form.placeholders.selectRecipient',
        selectBankDetails: 'invoices.form.placeholders.selectBankDetails',
        searchIssuers: 'invoices.form.placeholders.searchIssuers',
        searchBrands: 'invoices.form.placeholders.searchBrands',
        searchBankDetails: 'invoices.form.placeholders.searchBankDetails',
        notes: 'invoices.form.placeholders.notes',
        itemDescription: 'invoices.form.placeholders.itemDescription',
        recipientEmail: 'invoices.form.placeholders.recipientEmail',
        selectRecipientType: 'invoices.form.placeholders.selectRecipientType',
        selectIssueDate: 'invoices.form.placeholders.selectIssueDate',
        selectDueDate: 'invoices.form.placeholders.selectDueDate'
      },
      validation: {
        issuerRequired: 'invoices.form.validation.issuerRequired',
        recipientRequired: 'invoices.form.validation.recipientRequired',
        bankDetailsOptional: 'invoices.form.validation.bankDetailsOptional',
        invoiceNumberRequired: 'invoices.form.validation.invoiceNumberRequired',
        invoiceNumberTooLong: 'invoices.form.validation.invoiceNumberTooLong',
        currencyRequired: 'invoices.form.validation.currencyRequired',
        issueDateRequired: 'invoices.form.validation.issueDateRequired',
        dueDateRequired: 'invoices.form.validation.dueDateRequired',
        dueDateAfterIssue: 'invoices.form.validation.dueDateAfterIssue',
        notesTooLong: 'invoices.form.validation.notesTooLong',
        itemsRequired: 'invoices.form.validation.itemsRequired',
        itemDescriptionRequired: 'invoices.form.validation.itemDescriptionRequired',
        itemDescriptionTooLong: 'invoices.form.validation.itemDescriptionTooLong',
        itemQuantityRequired: 'invoices.form.validation.itemQuantityRequired',
        itemQuantityMin: 'invoices.form.validation.itemQuantityMin',
        itemUnitPriceRequired: 'invoices.form.validation.itemUnitPriceRequired',
        itemUnitPriceMin: 'invoices.form.validation.itemUnitPriceMin',
        itemVatRateRequired: 'invoices.form.validation.itemVatRateRequired',
        itemVatRateMin: 'invoices.form.validation.itemVatRateMin',
        itemVatRateMax: 'invoices.form.validation.itemVatRateMax',
        recipientsRequired: 'invoices.form.validation.recipientsRequired',
        emailRequired: 'invoices.form.validation.emailRequired',
        emailInvalid: 'invoices.form.validation.emailInvalid',
        emailTooLong: 'invoices.form.validation.emailTooLong',
        recipientTypeRequired: 'invoices.form.validation.recipientTypeRequired'
      }
    },
    validation: {
      invoiceNumberRequired: 'invoices.validation.invoiceNumberRequired',
      issueDateRequired: 'invoices.validation.issueDateRequired',
      dueDateRequired: 'invoices.validation.dueDateRequired',
      currencyRequired: 'invoices.validation.currencyRequired',
      itemsRequired: 'invoices.validation.itemsRequired',
      recipientsRequired: 'invoices.validation.recipientsRequired',
      descriptionRequired: 'invoices.validation.descriptionRequired',
      quantityRequired: 'invoices.validation.quantityRequired',
      quantityMin: 'invoices.validation.quantityMin',
      unitPriceRequired: 'invoices.validation.unitPriceRequired',
      unitPriceMin: 'invoices.validation.unitPriceMin',
      vatRateMin: 'invoices.validation.vatRateMin',
      vatRateMax: 'invoices.validation.vatRateMax',
      emailRequired: 'invoices.validation.emailRequired',
      emailInvalid: 'invoices.validation.emailInvalid',
      recipientTypeRequired: 'invoices.validation.recipientTypeRequired'
    }
  },
  brands: {
    title: 'brands.title',
    description: 'brands.description',
    createNew: 'brands.createNew',
    noBrands: 'brands.noBrands',
    noBrandsDescription: 'brands.noBrandsDescription',
    noBrandsForSearch: 'brands.noBrandsForSearch',
    noBrandsForSearchDescription: 'brands.noBrandsForSearchDescription',
    loading: 'brands.loading',
    error: 'brands.error',
    edit: 'brands.edit',
    delete: 'brands.delete',
    confirmDelete: 'brands.confirmDelete',
    deleteDescription: 'brands.deleteDescription',
    deleting: 'brands.deleting',
    creating: 'brands.creating',
    updating: 'brands.updating',
    createTitle: 'brands.createTitle',
    editTitle: 'brands.editTitle',
    save: 'brands.save',
    cancel: 'brands.cancel',
    name: 'brands.name',
    companyName: 'brands.companyName',
    email: 'brands.email',
    phone: 'brands.phone',
    website: 'brands.website',
    basicInfo: 'brands.basicInfo',
    addressInfo: 'brands.addressInfo',
    businessInfo: 'brands.businessInfo',
    addressStreet: 'brands.addressStreet',
    addressCity: 'brands.addressCity',
    addressPostalCode: 'brands.addressPostalCode',
    addressCountry: 'brands.addressCountry',
    vatNumber: 'brands.vatNumber',
    registrationNumber: 'brands.registrationNumber',
    contacts: 'brands.contacts',
    contact: 'brands.contact',
    contactName: 'brands.contactName',
    contactEmail: 'brands.contactEmail',
    contactNotes: 'brands.contactNotes',
    addContact: 'brands.addContact',
    noContacts: 'brands.noContacts',
    noContactsDescription: 'brands.noContactsDescription',
    required: 'brands.required',
    searchPlaceholder: 'brands.searchPlaceholder',
    filterByName: 'brands.filterByName',
    showingResults: 'brands.showingResults',
    page: 'brands.page',
    of: 'brands.of',
    previous: 'brands.previous',
    next: 'brands.next',
    validation: {
      nameRequired: 'brands.validation.nameRequired',
      nameTooLong: 'brands.validation.nameTooLong',
      companyNameRequired: 'brands.validation.companyNameRequired',
      companyNameTooLong: 'brands.validation.companyNameTooLong',
      emailInvalid: 'brands.validation.emailInvalid',
      emailTooLong: 'brands.validation.emailTooLong',
      phoneTooLong: 'brands.validation.phoneTooLong',
      websiteTooLong: 'brands.validation.websiteTooLong',
      addressStreetTooLong: 'brands.validation.addressStreetTooLong',
      addressCityTooLong: 'brands.validation.addressCityTooLong',
      addressPostalCodeTooLong: 'brands.validation.addressPostalCodeTooLong',
      addressCountryTooLong: 'brands.validation.addressCountryTooLong',
      vatNumberTooLong: 'brands.validation.vatNumberTooLong',
      registrationNumberTooLong: 'brands.validation.registrationNumberTooLong',
      contactNameRequired: 'brands.validation.contactNameRequired',
      contactNameTooLong: 'brands.validation.contactNameTooLong',
      contactEmailRequired: 'brands.validation.contactEmailRequired',
      contactEmailInvalid: 'brands.validation.contactEmailInvalid',
      contactEmailTooLong: 'brands.validation.contactEmailTooLong',
      contactNotesTooLong: 'brands.validation.contactNotesTooLong'
    }
  },
  common: {
    loading: 'common.loading',
    cancel: 'common.cancel',
    save: 'common.save',
    delete: 'common.delete',
    edit: 'common.edit',
    create: 'common.create',
    update: 'common.update',
    retry: 'common.retry',
    deleting: 'common.deleting',
    close: 'common.close',
    clearAll: 'common.clearAll'
  },
  collaborationHubs: {
    title: 'collaborationHubs.title',
    description: 'collaborationHubs.description',
    createNew: 'collaborationHubs.createNew',
    noHubs: 'collaborationHubs.noHubs',
    noHubsDescription: 'collaborationHubs.noHubsDescription',
    noHubsForSearch: 'collaborationHubs.noHubsForSearch',
    noHubsForSearchDescription: 'collaborationHubs.noHubsForSearchDescription',
    loading: 'collaborationHubs.loading',
    error: 'collaborationHubs.error',
    viewHub: 'collaborationHubs.viewHub',
    editHub: 'collaborationHubs.editHub',
    deleteHub: 'collaborationHubs.deleteHub',
    confirmDelete: 'collaborationHubs.confirmDelete',
    deleteDescription: 'collaborationHubs.deleteDescription',
    deleting: 'collaborationHubs.deleting',
    delete: 'collaborationHubs.delete',
    cancel: 'collaborationHubs.cancel',
    hubToDelete: 'collaborationHubs.hubToDelete',
    deleteWarning: 'collaborationHubs.deleteWarning',
    deleteWarningDetails: 'collaborationHubs.deleteWarningDetails',
    hubName: 'collaborationHubs.hubName',
    brandName: 'collaborationHubs.brandName',
    myRole: 'collaborationHubs.myRole',
    createdAt: 'collaborationHubs.createdAt',
    searchPlaceholder: 'collaborationHubs.searchPlaceholder',
    filterByName: 'collaborationHubs.filterByName',
    showingResults: 'collaborationHubs.showingResults',
    page: 'collaborationHubs.page',
    of: 'collaborationHubs.of',
    previous: 'collaborationHubs.previous',
    next: 'collaborationHubs.next',
    roles: {
      admin: 'collaborationHubs.roles.admin',
      content_creator: 'collaborationHubs.roles.content_creator',
      reviewer: 'collaborationHubs.roles.reviewer',
      reviewer_creator: 'collaborationHubs.roles.reviewer_creator'
    },
    invite: 'collaborationHubs.invite',
    unknown: 'collaborationHubs.unknown',
    participant: 'collaborationHubs.participant',
    participants: 'collaborationHubs.participants',
    created: 'collaborationHubs.created',
    updating: 'collaborationHubs.updating',
    updateSuccess: 'collaborationHubs.updateSuccess',
    updateError: 'collaborationHubs.updateError',
    open: 'collaborationHubs.open',
    createDialog: {
      title: 'collaborationHubs.createDialog.title',
      description: 'collaborationHubs.createDialog.description',
      hubName: 'collaborationHubs.createDialog.hubName',
      brand: 'collaborationHubs.createDialog.brand',
      selectBrand: 'collaborationHubs.createDialog.selectBrand',
      hubNameLabel: 'collaborationHubs.createDialog.hubNameLabel',
      hubNamePlaceholder: 'collaborationHubs.createDialog.hubNamePlaceholder',
      brandLabel: 'collaborationHubs.createDialog.brandLabel',
      brandPlaceholder: 'collaborationHubs.createDialog.brandPlaceholder',
      descriptionLabel: 'collaborationHubs.createDialog.descriptionLabel',
      descriptionPlaceholder: 'collaborationHubs.createDialog.descriptionPlaceholder',
      cancel: 'collaborationHubs.createDialog.cancel',
      creating: 'collaborationHubs.createDialog.creating',
      createHub: 'collaborationHubs.createDialog.createHub',
      successMessage: 'collaborationHubs.createDialog.successMessage',
      errorMessage: 'collaborationHubs.createDialog.errorMessage',
      validation: {
        hubNameRequired: 'collaborationHubs.createDialog.validation.hubNameRequired',
        hubNameTooLong: 'collaborationHubs.createDialog.validation.hubNameTooLong',
        brandRequired: 'collaborationHubs.createDialog.validation.brandRequired',
        descriptionTooLong: 'collaborationHubs.createDialog.validation.descriptionTooLong'
      }
    },
    editDialog: {
      title: 'collaborationHubs.editDialog.title',
      description: 'collaborationHubs.editDialog.description',
      updating: 'collaborationHubs.editDialog.updating',
      updateHub: 'collaborationHubs.editDialog.updateHub',
      successMessage: 'collaborationHubs.editDialog.successMessage',
      errorMessage: 'collaborationHubs.editDialog.errorMessage'
    },
    tabs: {
      posts: 'collaborationHubs.tabs.posts',
      chat: 'collaborationHubs.tabs.chat',
      briefs: 'collaborationHubs.tabs.briefs',
      overview: 'collaborationHubs.tabs.overview'
    },
    posts: {
      title: 'collaborationHubs.posts.title',
      searchPlaceholder: 'collaborationHubs.posts.searchPlaceholder',
      filterByStatus: 'collaborationHubs.posts.filterByStatus',
      allPosts: 'collaborationHubs.posts.allPosts',
      pendingReview: 'collaborationHubs.posts.pendingReview',
      approved: 'collaborationHubs.posts.approved',
      needsRework: 'collaborationHubs.posts.needsRework',
      createPost: 'collaborationHubs.posts.createPost',
      editPost: 'collaborationHubs.posts.editPost',
      deletePost: 'collaborationHubs.posts.deletePost',
      likes: 'collaborationHubs.posts.likes',
      reviewers: 'collaborationHubs.posts.reviewers',
      pending: 'collaborationHubs.posts.pending',
      rework: 'collaborationHubs.posts.rework',
      noPosts: 'collaborationHubs.posts.noPosts',
      noPostsDescription: 'collaborationHubs.posts.noPostsDescription',
      createFirstPost: 'collaborationHubs.posts.createFirstPost',
      loading: 'collaborationHubs.posts.loading',
      loadingMore: 'collaborationHubs.posts.loadingMore',
      error: 'collaborationHubs.posts.error',
      errorDescription: 'collaborationHubs.posts.errorDescription',
      retry: 'collaborationHubs.posts.retry',
      createdBy: 'collaborationHubs.posts.createdBy',
      createdAt: 'collaborationHubs.posts.createdAt',
      updatedAt: 'collaborationHubs.posts.updatedAt',
      status: {
        pending: 'collaborationHubs.posts.status.pending',
        approved: 'collaborationHubs.posts.status.approved',
        rework: 'collaborationHubs.posts.status.rework',
        all: 'collaborationHubs.posts.status.all'
      },
      filters: {
        types: {
          all: 'collaborationHubs.posts.filters.types.all',
          assignedToMe: 'collaborationHubs.posts.filters.types.assignedToMe',
          needsReview: 'collaborationHubs.posts.filters.types.needsReview',
          myPending: 'collaborationHubs.posts.filters.types.myPending',
          myApproved: 'collaborationHubs.posts.filters.types.myApproved',
          myRework: 'collaborationHubs.posts.filters.types.myRework',
          reviewedByMe: 'collaborationHubs.posts.filters.types.reviewedByMe'
        }
      },
      actions: {
        edit: 'collaborationHubs.posts.actions.edit',
        delete: 'collaborationHubs.posts.actions.delete',
        view: 'collaborationHubs.posts.actions.view'
      },
      confirmDelete: 'collaborationHubs.posts.confirmDelete',
      deleteDescription: 'collaborationHubs.posts.deleteDescription',
      deleting: 'collaborationHubs.posts.deleting',
      postDeleted: 'collaborationHubs.posts.postDeleted',
      failedToDelete: 'collaborationHubs.posts.failedToDelete',
      postCreated: 'collaborationHubs.posts.postCreated',
      postUpdated: 'collaborationHubs.posts.postUpdated',
      createPostDescription: 'collaborationHubs.posts.createPostDescription',
      editPostDescription: 'collaborationHubs.posts.editPostDescription',
      form: {
        media: 'collaborationHubs.posts.form.media',
        mediaDescription: 'collaborationHubs.posts.form.mediaDescription',
        caption: 'collaborationHubs.posts.form.caption',
        captionPlaceholder: 'collaborationHubs.posts.form.captionPlaceholder',
        captionDescription: 'collaborationHubs.posts.form.captionDescription',
        reviewerNotes: 'collaborationHubs.posts.form.reviewerNotes',
        reviewerNotesPlaceholder: 'collaborationHubs.posts.form.reviewerNotesPlaceholder',
        reviewerNotesDescription: 'collaborationHubs.posts.form.reviewerNotesDescription',
        reviewers: 'collaborationHubs.posts.form.reviewers',
        selectReviewers: 'collaborationHubs.posts.form.selectReviewers',
        reviewersDescription: 'collaborationHubs.posts.form.reviewersDescription',
        assignReviewers: 'collaborationHubs.posts.form.assignReviewers',
        assignReviewersDescription: 'collaborationHubs.posts.form.assignReviewersDescription',
        cancel: 'collaborationHubs.posts.form.cancel',
        save: 'collaborationHubs.posts.form.save',
        creating: 'collaborationHubs.posts.form.creating',
        updating: 'collaborationHubs.posts.form.updating'
      },
      review: {
        reviewPost: 'collaborationHubs.posts.review.reviewPost',
        updateReview: 'collaborationHubs.posts.review.updateReview',
        reviewDecision: 'collaborationHubs.posts.review.reviewDecision',
        approve: 'collaborationHubs.posts.review.approve',
        requestRework: 'collaborationHubs.posts.review.requestRework',
        reviewNotes: 'collaborationHubs.posts.review.reviewNotes',
        notesFromCreator: 'collaborationHubs.posts.review.notesFromCreator',
        notesOptional: 'collaborationHubs.posts.review.notesOptional',
        notesRequiredForRework: 'collaborationHubs.posts.review.notesRequiredForRework',
        notesPlaceholderApprove: 'collaborationHubs.posts.review.notesPlaceholderApprove',
        notesPlaceholderRework: 'collaborationHubs.posts.review.notesPlaceholderRework',
        submitReview: 'collaborationHubs.posts.review.submitReview',
        submitting: 'collaborationHubs.posts.review.submitting',
        reviewSubmitted: 'collaborationHubs.posts.review.reviewSubmitted',
        reviewFailed: 'collaborationHubs.posts.review.reviewFailed',
        statusPending: 'collaborationHubs.posts.review.statusPending',
        statusApproved: 'collaborationHubs.posts.review.statusApproved',
        statusRework: 'collaborationHubs.posts.review.statusRework'
      },
      comments: {
        title: 'collaborationHubs.posts.comments.title',
        noComments: 'collaborationHubs.posts.comments.noComments',
        beFirstToComment: 'collaborationHubs.posts.comments.beFirstToComment',
        writeComment: 'collaborationHubs.posts.comments.writeComment',
        postComment: 'collaborationHubs.posts.comments.postComment',
        posting: 'collaborationHubs.posts.comments.posting',
        editComment: 'collaborationHubs.posts.comments.editComment',
        deleteComment: 'collaborationHubs.posts.comments.deleteComment',
        confirmDeleteComment: 'collaborationHubs.posts.comments.confirmDeleteComment',
        deleteCommentDescription: 'collaborationHubs.posts.comments.deleteCommentDescription',
        deleting: 'collaborationHubs.posts.comments.deleting',
        commentDeleted: 'collaborationHubs.posts.comments.commentDeleted',
        commentPosted: 'collaborationHubs.posts.comments.commentPosted',
        commentUpdated: 'collaborationHubs.posts.comments.commentUpdated',
        loadMore: 'collaborationHubs.posts.comments.loadMore',
        loading: 'collaborationHubs.posts.comments.loading',
        loadingComments: 'collaborationHubs.posts.comments.loadingComments',
        failedToLoad: 'collaborationHubs.posts.comments.failedToLoad',
        tryAgain: 'collaborationHubs.posts.comments.tryAgain',
        comment: 'collaborationHubs.posts.comments.comment',
        commentsPlural: 'collaborationHubs.posts.comments.commentsPlural',
        edited: 'collaborationHubs.posts.comments.edited',
        save: 'collaborationHubs.posts.comments.save',
        cancel: 'collaborationHubs.posts.comments.cancel',
        saving: 'collaborationHubs.posts.comments.saving',
        ctrlEnterToPost: 'collaborationHubs.posts.comments.ctrlEnterToPost',
        edit: 'collaborationHubs.posts.comments.edit',
        delete: 'collaborationHubs.posts.comments.delete',
        failedToPost: 'collaborationHubs.posts.comments.failedToPost',
        failedToUpdate: 'collaborationHubs.posts.comments.failedToUpdate',
        failedToDelete: 'collaborationHubs.posts.comments.failedToDelete'
      }
    },
    chat: {
      channels: 'collaborationHubs.chat.channels',
      participants: 'collaborationHubs.chat.participants',
      viewMembers: 'collaborationHubs.chat.viewMembers',
      messagePlaceholder: 'collaborationHubs.chat.messagePlaceholder',
      send: 'collaborationHubs.chat.send',
      general: 'collaborationHubs.chat.general',
      adminsOnly: 'collaborationHubs.chat.adminsOnly',
      contentReview: 'collaborationHubs.chat.contentReview',
      workspace: 'collaborationHubs.chat.workspace',
      loading: 'collaborationHubs.chat.loading',
      error: 'collaborationHubs.chat.error',
      noMessages: 'collaborationHubs.chat.noMessages',
      noMessagesDescription: 'collaborationHubs.chat.noMessagesDescription',
      typing: 'collaborationHubs.chat.typing',
      edited: 'collaborationHubs.chat.edited',
      editMessage: 'collaborationHubs.chat.editMessage',
      deleteMessage: 'collaborationHubs.chat.deleteMessage',
      confirmDelete: 'collaborationHubs.chat.confirmDelete',
      deleteDescription: 'collaborationHubs.chat.deleteDescription',
      uploading: 'collaborationHubs.chat.uploading',
      uploadError: 'collaborationHubs.chat.uploadError',
      attachFile: 'collaborationHubs.chat.attachFile',
      addEmoji: 'collaborationHubs.chat.addEmoji',
      mentionSomeone: 'collaborationHubs.chat.mentionSomeone',
      sendingMessage: 'collaborationHubs.chat.sendingMessage',
      messageError: 'collaborationHubs.chat.messageError',
      retryMessage: 'collaborationHubs.chat.retryMessage',
      loadMore: 'collaborationHubs.chat.loadMore',
      loadingMessages: 'collaborationHubs.chat.loadingMessages',
      connectionError: 'collaborationHubs.chat.connectionError',
      reconnecting: 'collaborationHubs.chat.reconnecting',
      connected: 'collaborationHubs.chat.connected',
      disconnected: 'collaborationHubs.chat.disconnected',
      members: 'collaborationHubs.chat.members',
      failedToLoadMembers: 'collaborationHubs.chat.failedToLoadMembers',
      noMembers: 'collaborationHubs.chat.noMembers',
      createChannel: 'collaborationHubs.chat.createChannel',
      createCustomChannel: 'collaborationHubs.chat.createCustomChannel',
      channelName: 'collaborationHubs.chat.channelName',
      channelDescription: 'collaborationHubs.chat.channelDescription',
      selectParticipants: 'collaborationHubs.chat.selectParticipants',
      creating: 'collaborationHubs.chat.creating',
      create: 'collaborationHubs.chat.create',
      editChannel: 'collaborationHubs.chat.editChannel',
      deleteChannel: 'collaborationHubs.chat.deleteChannel',
      manageParticipants: 'collaborationHubs.chat.manageParticipants',
      channelSettings: 'collaborationHubs.chat.channelSettings',
      confirmDeleteChannel: 'collaborationHubs.chat.confirmDeleteChannel',
      deleteChannelDescription: 'collaborationHubs.chat.deleteChannelDescription',
      channelNamePlaceholder: 'collaborationHubs.chat.channelNamePlaceholder',
      channelDescriptionPlaceholder: 'collaborationHubs.chat.channelDescriptionPlaceholder',
      noParticipantsSelected: 'collaborationHubs.chat.noParticipantsSelected',
      participantsSelected: 'collaborationHubs.chat.participantsSelected',
      addParticipants: 'collaborationHubs.chat.addParticipants',
      removeParticipants: 'collaborationHubs.chat.removeParticipants',
      channelCreated: 'collaborationHubs.chat.channelCreated',
      channelDeleted: 'collaborationHubs.chat.channelDeleted',
      participantsAdded: 'collaborationHubs.chat.participantsAdded',
      participantsRemoved: 'collaborationHubs.chat.participantsRemoved',
      failedToCreateChannel: 'collaborationHubs.chat.failedToCreateChannel',
      failedToDeleteChannel: 'collaborationHubs.chat.failedToDeleteChannel',
      failedToUpdateParticipants: 'collaborationHubs.chat.failedToUpdateParticipants',
      customChannel: 'collaborationHubs.chat.customChannel',
      generalChannel: 'collaborationHubs.chat.generalChannel',
      onlyCreatorCanDelete: 'collaborationHubs.chat.onlyCreatorCanDelete',
      cannotDeleteGeneral: 'collaborationHubs.chat.cannotDeleteGeneral'
    },
    briefs: {
      searchPlaceholder: 'collaborationHubs.briefs.searchPlaceholder',
      filterByAccess: 'collaborationHubs.briefs.filterByAccess',
      allBriefs: 'collaborationHubs.briefs.allBriefs',
      allParticipants: 'collaborationHubs.briefs.allParticipants',
      contentCreators: 'collaborationHubs.briefs.contentCreators',
      reviewersAndAdmins: 'collaborationHubs.briefs.reviewersAndAdmins',
      adminsOnly: 'collaborationHubs.briefs.adminsOnly',
      createBrief: 'collaborationHubs.briefs.createBrief',
      noBriefs: 'collaborationHubs.briefs.noBriefs',
      noBriefsDescription: 'collaborationHubs.briefs.noBriefsDescription',
      createFirstBrief: 'collaborationHubs.briefs.createFirstBrief',
      updated: 'collaborationHubs.briefs.updated',
      created: 'collaborationHubs.briefs.created',
      accessLevels: {
        all: 'collaborationHubs.briefs.accessLevels.all',
        creators: 'collaborationHubs.briefs.accessLevels.creators',
        reviewers: 'collaborationHubs.briefs.accessLevels.reviewers',
        admins: 'collaborationHubs.briefs.accessLevels.admins'
      },
      dialog: {
        createTitle: 'collaborationHubs.briefs.dialog.createTitle',
        editTitle: 'collaborationHubs.briefs.dialog.editTitle',
        titleLabel: 'collaborationHubs.briefs.dialog.titleLabel',
        titlePlaceholder: 'collaborationHubs.briefs.dialog.titlePlaceholder',
        bodyLabel: 'collaborationHubs.briefs.dialog.bodyLabel',
        bodyPlaceholder: 'collaborationHubs.briefs.dialog.bodyPlaceholder',
        scopeLabel: 'collaborationHubs.briefs.dialog.scopeLabel',
        scopeDescription: 'collaborationHubs.briefs.dialog.scopeDescription',
        specificPeopleLabel: 'collaborationHubs.briefs.dialog.specificPeopleLabel',
        specificPeopleDescription: 'collaborationHubs.briefs.dialog.specificPeopleDescription',
        cancel: 'collaborationHubs.briefs.dialog.cancel',
        save: 'collaborationHubs.briefs.dialog.save',
        creating: 'collaborationHubs.briefs.dialog.creating',
        updating: 'collaborationHubs.briefs.dialog.updating',
        createSuccess: 'collaborationHubs.briefs.dialog.createSuccess',
        updateSuccess: 'collaborationHubs.briefs.dialog.updateSuccess'
      },
      viewDialog: {
        title: 'collaborationHubs.briefs.viewDialog.title',
        loading: 'collaborationHubs.briefs.viewDialog.loading',
        error: 'collaborationHubs.briefs.viewDialog.error',
        close: 'collaborationHubs.briefs.viewDialog.close',
        basicInfo: 'collaborationHubs.briefs.viewDialog.basicInfo',
        content: 'collaborationHubs.briefs.viewDialog.content',
        creatorInfo: 'collaborationHubs.briefs.viewDialog.creatorInfo',
        createdBy: 'collaborationHubs.briefs.viewDialog.createdBy',
        createdAt: 'collaborationHubs.briefs.viewDialog.createdAt',
        updatedAt: 'collaborationHubs.briefs.viewDialog.updatedAt',
        noContent: 'collaborationHubs.briefs.viewDialog.noContent'
      },
      scopes: {
        allParticipants: 'collaborationHubs.briefs.scopes.allParticipants',
        adminsReviewers: 'collaborationHubs.briefs.scopes.adminsReviewers',
        adminsOnly: 'collaborationHubs.briefs.scopes.adminsOnly',
        customSelection: 'collaborationHubs.briefs.scopes.customSelection'
      },
      validation: {
        titleRequired: 'collaborationHubs.briefs.validation.titleRequired',
        titleMaxLength: 'collaborationHubs.briefs.validation.titleMaxLength',
        bodyMaxLength: 'collaborationHubs.briefs.validation.bodyMaxLength'
      },
      actions: {
        edit: 'collaborationHubs.briefs.actions.edit',
        delete: 'collaborationHubs.briefs.actions.delete',
        confirmDelete: 'collaborationHubs.briefs.actions.confirmDelete',
        confirmDeleteDescription: 'collaborationHubs.briefs.actions.confirmDeleteDescription',
        deleteSuccess: 'collaborationHubs.briefs.actions.deleteSuccess'
      }
    },
    overview: {
      totalPosts: 'collaborationHubs.overview.totalPosts',
      pendingReviews: 'collaborationHubs.overview.pendingReviews',
      approved: 'collaborationHubs.overview.approved',
      participants: 'collaborationHubs.overview.participants',
      approvalRate: 'collaborationHubs.overview.approvalRate',
      contentApproval: 'collaborationHubs.overview.contentApproval',
      approvedOutOf: 'collaborationHubs.overview.approvedOutOf',
      participationRate: 'collaborationHubs.overview.participationRate',
      activeParticipants: 'collaborationHubs.overview.activeParticipants',
      activeOutOf: 'collaborationHubs.overview.activeOutOf',
      recentActivity: 'collaborationHubs.overview.recentActivity',
      messagesSent: 'collaborationHubs.overview.messagesSent',
      briefsCreated: 'collaborationHubs.overview.briefsCreated',
      needsRework: 'collaborationHubs.overview.needsRework',
      justNow: 'collaborationHubs.overview.justNow',
      hoursAgo: 'collaborationHubs.overview.hoursAgo',
      daysAgo: 'collaborationHubs.overview.daysAgo',
      activities: {
        created: 'collaborationHubs.overview.activities.created',
        approved: 'collaborationHubs.overview.activities.approved',
        commentedOn: 'collaborationHubs.overview.activities.commentedOn',
        joinedHub: 'collaborationHubs.overview.activities.joinedHub',
        updated: 'collaborationHubs.overview.activities.updated'
      }
    },
    mediaCarousel: {
      video: 'collaborationHubs.mediaCarousel.video',
      mediaAlt: 'collaborationHubs.mediaCarousel.mediaAlt'
    },
    manageParticipants: {
      title: 'collaborationHubs.manageParticipants.title',
      description: 'collaborationHubs.manageParticipants.description',
      inviteTab: 'collaborationHubs.manageParticipants.inviteTab',
      manageTab: 'collaborationHubs.manageParticipants.manageTab',
      participantType: 'collaborationHubs.manageParticipants.participantType',
      externalUser: 'collaborationHubs.manageParticipants.externalUser',
      brandContact: 'collaborationHubs.manageParticipants.brandContact',
      emailAddress: 'collaborationHubs.manageParticipants.emailAddress',
      name: 'collaborationHubs.manageParticipants.name',
      nameOptional: 'collaborationHubs.manageParticipants.nameOptional',
      selectContact: 'collaborationHubs.manageParticipants.selectContact',
      loadingContacts: 'collaborationHubs.manageParticipants.loadingContacts',
      searchContacts: 'collaborationHubs.manageParticipants.searchContacts',
      noContactsFound: 'collaborationHubs.manageParticipants.noContactsFound',
      noContactsForBrand: 'collaborationHubs.manageParticipants.noContactsForBrand',
      role: 'collaborationHubs.manageParticipants.role',
      selectRole: 'collaborationHubs.manageParticipants.selectRole',
      externalInviteInfo: 'collaborationHubs.manageParticipants.externalInviteInfo',
      brandContactInviteInfo: 'collaborationHubs.manageParticipants.brandContactInviteInfo',
      sendInvitation: 'collaborationHubs.manageParticipants.sendInvitation',
      sendingInvitation: 'collaborationHubs.manageParticipants.sendingInvitation',
      cancel: 'collaborationHubs.manageParticipants.cancel',
      close: 'collaborationHubs.manageParticipants.close',
      noParticipants: 'collaborationHubs.manageParticipants.noParticipants',
      noParticipantsDescription: 'collaborationHubs.manageParticipants.noParticipantsDescription',
      inviteParticipants: 'collaborationHubs.manageParticipants.inviteParticipants',
      loadingParticipants: 'collaborationHubs.manageParticipants.loadingParticipants',
      external: 'collaborationHubs.manageParticipants.external',
      makeAdmin: 'collaborationHubs.manageParticipants.makeAdmin',
      contentCreator: 'collaborationHubs.manageParticipants.contentCreator',
      reviewer: 'collaborationHubs.manageParticipants.reviewer',
      reviewerCreator: 'collaborationHubs.manageParticipants.reviewerCreator',
      resendInvitation: 'collaborationHubs.manageParticipants.resendInvitation',
      remove: 'collaborationHubs.manageParticipants.remove',
      managingCount: 'collaborationHubs.manageParticipants.managingCount'
    }
  },
  validation: {
    required: 'validation.required',
    email: {
      invalid: 'validation.email.invalid',
      tooLong: 'validation.email.tooLong'
    },
    password: {
      tooShort: 'validation.password.tooShort',
      tooLong: 'validation.password.tooLong',
      requirements: 'validation.password.requirements'
    },
    accountName: {
      tooShort: 'validation.accountName.tooShort',
      tooLong: 'validation.accountName.tooLong'
    },
    displayName: {
      required: 'validation.displayName.required',
      tooLong: 'validation.displayName.tooLong'
    }
  },
  placeholders: {
    accountName: 'placeholders.accountName',
    displayName: 'placeholders.displayName',
    email: 'placeholders.email',
    password: 'placeholders.password'
  },
  messages: {
    emailSentTo: 'messages.emailSentTo'
  },
  ui: {
    emojiPicker: {
      addEmoji: 'ui.emojiPicker.addEmoji',
      categories: {
        smileysAndPeople: 'ui.emojiPicker.categories.smileysAndPeople',
        animalsAndNature: 'ui.emojiPicker.categories.animalsAndNature',
        foodAndDrink: 'ui.emojiPicker.categories.foodAndDrink',
        activities: 'ui.emojiPicker.categories.activities',
        travelAndPlaces: 'ui.emojiPicker.categories.travelAndPlaces',
        objects: 'ui.emojiPicker.categories.objects',
        symbols: 'ui.emojiPicker.categories.symbols'
      }
    },
    textareaWithEmoji: {
      emojiInserted: 'ui.textareaWithEmoji.emojiInserted'
    },
    reviewerMultiSelect: {
      selectReviewers: 'ui.reviewerMultiSelect.selectReviewers',
      searchReviewers: 'ui.reviewerMultiSelect.searchReviewers',
      loadingReviewers: 'ui.reviewerMultiSelect.loadingReviewers',
      noReviewersFound: 'ui.reviewerMultiSelect.noReviewersFound',
      noReviewersAvailable: 'ui.reviewerMultiSelect.noReviewersAvailable',
      reviewersSelected: 'ui.reviewerMultiSelect.reviewersSelected',
      reviewer: 'ui.reviewerMultiSelect.reviewer',
      reviewers: 'ui.reviewerMultiSelect.reviewers'
    },
    mentionInput: {
      loadingParticipants: 'ui.mentionInput.loadingParticipants',
      noParticipantsFound: 'ui.mentionInput.noParticipantsFound'
    },
    userProfilePopup: {
      unknownUser: 'ui.userProfilePopup.unknownUser',
      externalUser: 'ui.userProfilePopup.externalUser',
      sendEmail: 'ui.userProfilePopup.sendEmail'
    }
  },
  navigation: {
    userProfile: {
      logout: 'navigation.userProfile.logout',
      loggingOut: 'navigation.userProfile.loggingOut'
    }
  },
  settings: {
    title: 'settings.title',
    description: 'settings.description',
    profile: {
      title: 'settings.profile.title',
      description: 'settings.profile.description',
      name: 'settings.profile.name',
      email: 'settings.profile.email',
      accountType: 'settings.profile.accountType',
      internalUser: 'settings.profile.internalUser',
      externalParticipant: 'settings.profile.externalParticipant',
      multiAccountAccess: 'settings.profile.multiAccountAccess',
      notSet: 'settings.profile.notSet'
    },
    notifications: {
      title: 'settings.notifications.title',
      description: 'settings.notifications.description',
      externalDescription: 'settings.notifications.externalDescription',
      savingChanges: 'settings.notifications.savingChanges',
      categories: {
        collaboration: 'settings.notifications.categories.collaboration',
        content: 'settings.notifications.categories.content',
        communication: 'settings.notifications.categories.communication'
      },
      types: {
        inviteToHub: {
          label: 'settings.notifications.types.inviteToHub.label',
          description: 'settings.notifications.types.inviteToHub.description'
        },
        assignedAsReviewer: {
          label: 'settings.notifications.types.assignedAsReviewer.label',
          description: 'settings.notifications.types.assignedAsReviewer.description'
        },
        postReviewed: {
          label: 'settings.notifications.types.postReviewed.label',
          description: 'settings.notifications.types.postReviewed.description'
        },
        commentAdded: {
          label: 'settings.notifications.types.commentAdded.label',
          description: 'settings.notifications.types.commentAdded.description'
        },
        commentMention: {
          label: 'settings.notifications.types.commentMention.label',
          description: 'settings.notifications.types.commentMention.description'
        },
        chatMention: {
          label: 'settings.notifications.types.chatMention.label',
          description: 'settings.notifications.types.chatMention.description'
        },
        chatAdded: {
          label: 'settings.notifications.types.chatAdded.label',
          description: 'settings.notifications.types.chatAdded.description'
        }
      },
      channels: {
        inApp: {
          label: 'settings.notifications.channels.inApp.label',
          description: 'settings.notifications.channels.inApp.description'
        },
        email: {
          label: 'settings.notifications.channels.email.label',
          description: 'settings.notifications.channels.email.description'
        }
      },
      badges: {
        type: 'settings.notifications.badges.type',
        types: 'settings.notifications.badges.types'
      }
    }
  }
} as const;

/**
 * Simple function that returns the pre-built translation keys object.
 * No complex proxy needed - just return the static keys.
 */
function getTranslationKeys(): TypedTranslationKeys {
  return translationKeys;
}

/**
 * Hook that provides type-safe access to translations with the new pattern.
 *
 * Usage:
 * const { t, keys } = useTypedTranslations();
 * const title = t(keys.brands.title);
 * const count = t(keys.brands.count, { count: 1 });
 *
 * @returns Object with translation function and typed keys
 */
export function useTypedTranslations() {
  const { t: originalT } = useTranslation();

  // Get the pre-built keys object
  const keys = getTranslationKeys();

  // Enhanced translation function that accepts key strings and parameters
  const t = (key: string, params?: Record<string, unknown>): string => {
    return originalT(key, params);
  };

  return { t, keys };
}

// Type for the enhanced useTranslations hook return value
export interface UseTranslationsReturn {
  readonly t: (key: string, params?: Record<string, unknown>) => string;
  readonly keys: TypedTranslationKeys;
  readonly i18n: ReturnType<typeof useTranslation>['i18n'];
  readonly exists: (key: string) => boolean;
  readonly optional: (key: string, fallback?: string) => string;
}

/**
 * Enhanced hook that returns the new typed translation pattern
 * and additional utilities.
 *
 * Pattern: t(keys.brands.title) or t(keys.brands.count, { count: 1 })
 *
 * @returns Object with translation function, typed keys, and utilities
 */
export function useTranslations(): UseTranslationsReturn {
  const { t: originalT, i18n } = useTranslation();
  const { t, keys } = useTypedTranslations();

  return {
    // New pattern: t(keys.brands.title) or t(keys.brands.count, { count: 1 })
    t,
    keys,

    // Access to i18n instance for advanced usage
    i18n,

    // Utility function to check if a translation exists
    exists: (key: string): boolean => {
      const translation = originalT(key);
      return translation !== key;
    },

    // Utility function for conditional translations
    optional: (key: string, fallback?: string): string => {
      const translation = originalT(key);
      return translation !== key ? translation : (fallback || '');
    }
  };
}

/**
 * Utility function to get a translation key path as a string.
 * Useful for cases where you need the actual key string.
 *
 * Usage:
 * const keyPath = getTranslationKey((keys) => keys.auth.login.title);
 * // Returns: 'auth.login.title'
 */
export function getTranslationKey(
  accessor: (keys: TypedTranslationKeys) => string
): string {
  const keys = getTranslationKeys();
  const result = accessor(keys);
  return result;
}
