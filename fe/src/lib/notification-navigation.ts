import { useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { useMarkNotificationAsRead } from '@/hooks/use-notifications';
import type { components } from '@/lib/api/v1';
import { NotificationResponseType, NotificationResponseStatus } from '@/lib/api/v1';

type NotificationResponse = components['schemas']['NotificationResponse'];

/**
 * Utility functions for notification deep linking and navigation.
 * Handles generating deep links and navigating to notification resources.
 */

/**
 * Generates a deep link URL for a notification based on its type and entity references.
 */
export function generateNotificationDeepLink(notification: NotificationResponse): string {
  const hubId = notification.collaborationHubId;
  
  if (!hubId) {
    // If no hub ID, navigate to dashboard
    return '/app/dashboard';
  }

  const baseUrl = `/app/collaboration-hubs/${hubId}`;

  switch (notification.type) {
    case NotificationResponseType.CHAT_MENTION:
    case NotificationResponseType.CHAT_ADDED:
      if (notification.chatChannelId) {
        return `${baseUrl}?tab=chat&chat=${notification.chatChannelId}`;
      }
      return `${baseUrl}?tab=chat`;

    case NotificationResponseType.COMMENT_ADDED:
    case NotificationResponseType.COMMENT_MENTION:
    case NotificationResponseType.POST_REVIEWED:
    case NotificationResponseType.ASSIGNED_AS_REVIEWER:
      if (notification.postId) {
        if (notification.commentId) {
          return `${baseUrl}?tab=posts&post=${notification.postId}#comment-${notification.commentId}`;
        }
        return `${baseUrl}?tab=posts&post=${notification.postId}`;
      }
      return `${baseUrl}?tab=posts`;

    case NotificationResponseType.BRIEF_CREATED:
    case NotificationResponseType.BRIEF_UPDATED:
    case NotificationResponseType.BRIEF_ASSIGNED:
      if (notification.briefId) {
        return `${baseUrl}?tab=briefs&brief=${notification.briefId}`;
      }
      return `${baseUrl}?tab=briefs`;

    case NotificationResponseType.INVITE_TO_HUB:
    default:
      return baseUrl;
  }
}

/**
 * Hook for handling notification navigation with automatic read marking.
 * Implements optimistic updates for immediate visual feedback.
 */
export function useNotificationNavigation() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const markAsRead = useMarkNotificationAsRead();

  /**
   * Optimistically updates notification status in the cache.
   */
  const optimisticallyMarkAsRead = (notificationId: number) => {
    // Update notifications list cache
    queryClient.setQueryData(
      ['get', '/api/notifications'],
      (oldData: any) => {
        if (!oldData?.data?.content) return oldData;

        return {
          ...oldData,
          data: {
            ...oldData.data,
            content: oldData.data.content.map((notification: NotificationResponse) =>
              notification.id === notificationId
                ? { ...notification, status: NotificationResponseStatus.READ, readAt: new Date().toISOString() }
                : notification
            ),
          },
        };
      }
    );

    // Update unread count cache
    queryClient.setQueryData(
      ['get', '/api/notifications/unread-count'],
      (oldCount: any) => {
        const currentCount = oldCount?.data || 0;
        return {
          ...oldCount,
          data: Math.max(0, currentCount - 1),
        };
      }
    );
  };

  /**
   * Navigates to a notification's target resource and marks it as read.
   */
  const navigateToNotification = (notification: NotificationResponse) => {
    // Generate the deep link URL
    const url = generateNotificationDeepLink(notification);

    // Optimistically mark as read for immediate visual feedback
    if (notification.status === NotificationResponseStatus.UNREAD && notification.id) {
      optimisticallyMarkAsRead(notification.id);

      // Make the API call in the background
      markAsRead.mutate({
        body: { notificationId: notification.id },
      });
    }

    // Navigate to the target URL
    navigate(url);
  };

  /**
   * Marks a notification as read without navigation.
   */
  const markAsReadOnly = (notification: NotificationResponse) => {

    if (notification.status === NotificationResponseStatus.UNREAD && notification.id) {
      // Optimistically mark as read for immediate visual feedback
      optimisticallyMarkAsRead(notification.id);

      // Make the API call in the background
      markAsRead.mutate({
        body: { notificationId: notification.id },
      });
    }
  };

  return {
    navigateToNotification,
    markAsReadOnly,
    isLoading: markAsRead.isPending,
  };
}

/**
 * Utility functions for parsing URL parameters for deep linking.
 */
export const DeepLinkUtils = {
  /**
   * Gets the active tab from URL search params.
   */
  getActiveTab: (searchParams: URLSearchParams): string => {
    return searchParams.get('tab') || 'posts';
  },

  /**
   * Gets the post ID to open from URL search params.
   */
  getPostToOpen: (searchParams: URLSearchParams): number | null => {
    const postId = searchParams.get('post');
    return postId ? parseInt(postId, 10) : null;
  },

  /**
   * Gets the chat channel ID to select from URL search params.
   */
  getChatToSelect: (searchParams: URLSearchParams): number | null => {
    const chatId = searchParams.get('chat');
    return chatId ? parseInt(chatId, 10) : null;
  },

  /**
   * Gets the brief ID to focus from URL search params.
   */
  getBriefToFocus: (searchParams: URLSearchParams): number | null => {
    const briefId = searchParams.get('brief');
    return briefId ? parseInt(briefId, 10) : null;
  },

  /**
   * Gets the comment ID to scroll to from URL hash.
   */
  getCommentToScrollTo: (hash: string): number | null => {
    const match = hash.match(/^#comment-(\d+)$/);
    return match ? parseInt(match[1], 10) : null;
  },

  /**
   * Updates URL search params while preserving other parameters.
   */
  updateSearchParams: (
    searchParams: URLSearchParams,
    updates: Record<string, string | null>
  ): URLSearchParams => {
    const newParams = new URLSearchParams(searchParams);
    
    Object.entries(updates).forEach(([key, value]) => {
      if (value === null) {
        newParams.delete(key);
      } else {
        newParams.set(key, value);
      }
    });

    return newParams;
  },
};
