import { useEffect, useState } from 'react';
import { useSearchParams, Navigate } from 'react-router';
import { useMagicLinkAuth } from '@/hooks/auth/use-magic-link-auth';
import { useAuth, AuthStatus } from '@/hooks/auth';
import { LoadingScreen } from '@/components/loading-screen';
import { ROUTES } from '@/router/routes';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';

/**
 * Magic Link Authentication Page
 * 
 * Handles the magic link authentication flow for external participants.
 * Extracts the token from URL parameters, authenticates the user,
 * and redirects them to the appropriate collaboration hub.
 * 
 * URL format: /auth/magic-link?token=abc123
 */
export default function MagicLinkAuthPage() {
  const [searchParams] = useSearchParams();
  const { status,  isAuthenticated } = useAuth();

  const {
    authenticateWithMagicLink,
    isPending,
    error,
    isError,
    isSuccess,
    reset
  } = useMagicLinkAuth();

  const [authenticationAttempted, setAuthenticationAttempted] = useState(false);
  const token = searchParams.get('token');

  // Attempt authentication when component mounts and token is available
  useEffect(() => {
    if (token && !authenticationAttempted && status !== AuthStatus.LOADING) {
      setAuthenticationAttempted(true);
      authenticateWithMagicLink({
        params: { query: { token } }
      });
    }
  }, [token, authenticationAttempted, status, authenticateWithMagicLink]);

  // Redirect authenticated users to dashboard (fallback, shouldn't happen with new redirect logic)
  if (isAuthenticated) {
    return <Navigate to={ROUTES.DASHBOARD} replace />;
  }

  // Show loading screen during authentication attempt
  if (isPending || status === AuthStatus.LOADING) {
    return (
      <LoadingScreen
        message="Authenticating..."
      />
    );
  }

  // Handle missing token
  if (!token) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <XCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              Invalid Magic Link
            </CardTitle>
            <CardDescription>
              The magic link appears to be incomplete or corrupted.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => window.location.href = ROUTES.LOGIN}
              className="w-full"
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Handle authentication error
  if (isError && error) {
    const errorMessage = error.error?.message || '';
    const isTokenExpired = errorMessage.includes('expired') || errorMessage.includes('invalid');
    
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <XCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              {isTokenExpired
                ? "Link Expired"
                : "Authentication Failed"
              }
            </CardTitle>
            <CardDescription>
              {isTokenExpired
                ? "This magic link has expired. Please request a new invitation."
                : "We couldn't authenticate you with this link. Please try again or request a new invitation."
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {errorMessage || "An unexpected error occurred during authentication."}
              </AlertDescription>
            </Alert>
            <div className="flex flex-col space-y-2">
              <Button
                onClick={() => {
                  reset();
                  setAuthenticationAttempted(false);
                }}
                variant="outline"
                className="w-full"
              >
                Try Again
              </Button>
              <Button
                onClick={() => window.location.href = ROUTES.LOGIN}
                className="w-full"
              >
                Go to Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Success state (should redirect, but show success message briefly)
  if (isSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              Authentication Successful!
            </CardTitle>
            <CardDescription>
              You've been successfully authenticated. Redirecting you to your destination...
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  // Default loading state (shouldn't reach here, but safety net)
  return (
    <LoadingScreen
      message="Processing your invitation..."
    />
  );
}
