/**
 * Chat hooks for the Collaboration Hub frontend.
 *
 * These hooks provide a clean interface for chat operations
 * using the openapi-react-query pattern with proper TypeScript typing.
 *
 * Channel Management:
 * - useChatChannels: Fetch channels for a hub with permissions
 * - useChatChannel: Fetch specific channel details
 *
 * Message Management:
 * - useChatMessages: Fetch messages with infinite scroll pagination
 * - useSendMessage: Send new messages with attachments and mentions
 * - useUpdateMessage: Edit existing messages
 * - useDeleteMessage: Delete messages
 * - flattenMessages: Utility to flatten infinite query pages
 *
 * File Upload:
 * - useUploadChatAttachment: Upload files for chat attachments
 *
 * All hooks include proper error handling, loading states, and automatic
 * multi-tenancy scoping handled by the backend.
 */

export {
  useChatChannels,
  useChatChannel,
  useCreateChannel,
  useDeleteChannel,
  useAddChannelParticipants,
  useRemoveChannelParticipants
} from './use-chat-channels';
export { useChatMessages, flattenMessages } from './use-chat-messages';
export { useSendMessage, useUpdateMessage, useDeleteMessage } from './use-send-message';
export { useUploadChatAttachment } from './use-upload-chat-attachment';
export { useChannelMembers } from './use-channel-members';
