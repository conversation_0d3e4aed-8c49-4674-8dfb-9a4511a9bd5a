{"name": "fe", "private": true, "version": "0.0.0", "type": "module", "proxy": "http://localhost:8080", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate": "openapi-typescript --enum http://localhost:8080/v3/api-docs -o ./src/lib/api/v1.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@stomp/stompjs": "^7.1.1", "@tabler/icons-react": "^3.34.0", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query-devtools": "^5.80.3", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "i18next": "^25.2.1", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "openapi-fetch": "^0.14.0", "openapi-react-query": "^0.5.0", "react": "^18.2.0", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-hook-form": "^7.57.0", "react-i18next": "^15.5.2", "react-resizable-panels": "^3.0.3", "react-router": "^7.6.2", "react-router-dom": "^7.7.1", "recharts": "^2.15.3", "sockjs-client": "^1.6.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "vaul": "^1.1.2", "yup": "^1.6.1", "zod": "^3.25.51"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.29", "@types/react": "^18.2.74", "@types/react-dom": "^18.2.22", "@types/sockjs-client": "^1.5.4", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "openapi-typescript": "^7.8.0", "tw-animate-css": "^1.3.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}