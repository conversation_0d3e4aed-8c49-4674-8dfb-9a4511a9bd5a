const fs = require('fs');
const path = require('path');

// List of files with unused translation imports
const filesToFix = [
  'src/hooks/bank-details/use-create-bank-detail.ts',
  'src/hooks/bank-details/use-delete-bank-detail.ts',
  'src/hooks/bank-details/use-update-bank-detail.ts',
  'src/hooks/brands/use-create-brand.ts',
  'src/hooks/brands/use-delete-brand.ts',
  'src/hooks/brands/use-update-brand.ts'
];

function fixUnusedTranslationImports(filePath) {
  try {
    const fullPath = path.join(__dirname, filePath);
    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Remove the useTranslations import line
    content = content.replace(/import { useTranslations } from '@\/lib\/i18n\/typed-translations';\n/g, '');
    
    // Remove the translation destructuring line
    content = content.replace(/\s*const { t, keys } = useTranslations\(\);\n/g, '\n');
    
    fs.writeFileSync(fullPath, content);
    console.log(`Fixed: ${filePath}`);
  } catch (err) {
    console.error(`Error fixing ${filePath}:`, err.message);
  }
}

// Fix all files
filesToFix.forEach(fixUnusedTranslationImports);

console.log('Finished fixing unused translation imports');
